package com.periodpal.app.data.entities

import androidx.room.Entity
import androidx.room.PrimaryKey
import kotlinx.datetime.LocalDate

@Entity(tableName = "cycle_entries")
data class CycleEntry(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val date: LocalDate,
    val isPeriodDay: Boolean = false,
    val isOvulationDay: Boolean = false,
    val cycleDay: Int? = null,
    val notes: String? = null,
    val createdAt: kotlinx.datetime.Instant = kotlinx.datetime.Clock.System.now(),
    val updatedAt: kotlinx.datetime.Instant = kotlinx.datetime.Clock.System.now()
)
