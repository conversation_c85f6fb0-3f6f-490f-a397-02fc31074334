package com.periodpal.app

import android.app.Application
import com.periodpal.app.notifications.NotificationHelper
import com.periodpal.app.notifications.NotificationScheduler
import dagger.hilt.android.HiltAndroidApp
import javax.inject.Inject

@HiltAndroidApp
class PeriodPalApplication : Application() {

    @Inject
    lateinit var notificationHelper: NotificationHelper

    override fun onCreate() {
        super.onCreate()

        // Initialize notification channels
        notificationHelper

        // Schedule periodic notifications
        NotificationScheduler.scheduleNotifications(this)
    }
}
