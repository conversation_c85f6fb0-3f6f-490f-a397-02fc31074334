package com.periodpal.app.di

import android.content.Context
import androidx.room.Room
import com.periodpal.app.data.dao.CycleEntryDao
import com.periodpal.app.data.dao.EmergencyContactDao
import com.periodpal.app.data.dao.SymptomEntryDao
import com.periodpal.app.data.dao.UserPreferencesDao
import com.periodpal.app.data.database.PeriodPalDatabase
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object DatabaseModule {
    
    @Provides
    @Singleton
    fun provideDatabase(@ApplicationContext context: Context): PeriodPalDatabase {
        return Room.databaseBuilder(
            context.applicationContext,
            PeriodPalDatabase::class.java,
            "period_pal_database"
        )
            .fallbackToDestructiveMigration()
            .build()
    }
    
    @Provides
    fun provideCycleEntryDao(database: PeriodPalDatabase): CycleEntryDao {
        return database.cycleEntryDao()
    }
    
    @Provides
    fun provideSymptomEntryDao(database: PeriodPalDatabase): SymptomEntryDao {
        return database.symptomEntryDao()
    }
    
    @Provides
    fun provideUserPreferencesDao(database: PeriodPalDatabase): UserPreferencesDao {
        return database.userPreferencesDao()
    }
    
    @Provides
    fun provideEmergencyContactDao(database: PeriodPalDatabase): EmergencyContactDao {
        return database.emergencyContactDao()
    }
}
