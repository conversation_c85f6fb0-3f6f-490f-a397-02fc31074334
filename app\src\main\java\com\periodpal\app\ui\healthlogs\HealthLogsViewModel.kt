package com.periodpal.app.ui.healthlogs

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.periodpal.app.data.entities.SymptomEntry
import com.periodpal.app.data.entities.SymptomType
import com.periodpal.app.data.repository.CycleRepository
import com.periodpal.app.ui.components.SymptomOption
import com.periodpal.app.ui.components.SymptomOptions
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.launch
import kotlinx.datetime.Clock
import kotlinx.datetime.DateTimeUnit
import kotlinx.datetime.LocalDate
import kotlinx.datetime.TimeZone
import kotlinx.datetime.minus
import kotlinx.datetime.todayIn
import javax.inject.Inject

@HiltViewModel
class HealthLogsViewModel @Inject constructor(
    private val cycleRepository: CycleRepository
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(HealthLogsUiState())
    val uiState: StateFlow<HealthLogsUiState> = _uiState.asStateFlow()
    
    private val today = Clock.System.todayIn(TimeZone.currentSystemDefault())
    
    init {
        loadHealthLogs()
    }
    
    private fun loadHealthLogs() {
        viewModelScope.launch {
            combine(
                cycleRepository.getAllSymptomEntries(),
                cycleRepository.getAllCycleEntries()
            ) { symptomEntries, cycleEntries ->
                
                // Group symptoms by date
                val symptomsByDate = symptomEntries.groupBy { it.date }
                    .toSortedMap(compareByDescending { it })
                
                // Get recent entries (last 30 days)
                val thirtyDaysAgo = today.minus(30, DateTimeUnit.DAY)
                val recentEntries = symptomsByDate.filterKeys { it >= thirtyDaysAgo }
                
                // Create daily logs
                val dailyLogs = recentEntries.map { (date, symptoms) ->
                    val cycleEntry = cycleEntries.find { it.date == date }
                    DailyHealthLog(
                        date = date,
                        isPeriodDay = cycleEntry?.isPeriodDay == true,
                        symptoms = symptoms,
                        hasData = symptoms.isNotEmpty() || cycleEntry != null
                    )
                }
                
                // Get today's symptoms for quick logging
                val todaysSymptoms = symptomsByDate[today] ?: emptyList()
                val currentSymptoms = mapCurrentSymptoms(todaysSymptoms)
                
                HealthLogsUiState(
                    isLoading = false,
                    dailyLogs = dailyLogs,
                    selectedDate = today,
                    currentSymptoms = currentSymptoms,
                    availableSymptomTypes = getAllAvailableSymptomTypes()
                )
            }.collect { newState ->
                _uiState.value = newState
            }
        }
    }
    
    private fun mapCurrentSymptoms(symptoms: List<SymptomEntry>): Map<SymptomType, SymptomOption?> {
        val currentSymptoms = mutableMapOf<SymptomType, SymptomOption?>()
        
        // Map flow intensity
        val flowSymptom = symptoms.find { it.symptomType == SymptomType.FLOW_INTENSITY }
        currentSymptoms[SymptomType.FLOW_INTENSITY] = flowSymptom?.let { symptom ->
            SymptomOptions.flowOptions.find { it.value == symptom.value }
        }
        
        // Map mood
        val moodSymptom = symptoms.find { it.symptomType == SymptomType.MOOD }
        currentSymptoms[SymptomType.MOOD] = moodSymptom?.let { symptom ->
            SymptomOptions.moodOptions.find { it.value == symptom.value }
        }
        
        // Map pain level
        val painSymptom = symptoms.find { it.symptomType == SymptomType.PAIN_LEVEL }
        currentSymptoms[SymptomType.PAIN_LEVEL] = painSymptom?.let { symptom ->
            SymptomOptions.painOptions.find { it.value == symptom.value }
        }
        
        // Map other symptoms
        SymptomType.values().forEach { type ->
            if (!currentSymptoms.containsKey(type)) {
                val symptom = symptoms.find { it.symptomType == type }
                currentSymptoms[type] = symptom?.let { 
                    createSymptomOptionFromEntry(it)
                }
            }
        }
        
        return currentSymptoms
    }
    
    private fun createSymptomOptionFromEntry(entry: SymptomEntry): SymptomOption {
        return when (entry.symptomType) {
            SymptomType.CRAMPS -> SymptomOptions.painOptions.find { it.value == entry.value } 
                ?: SymptomOptions.painOptions.first()
            SymptomType.HEADACHE -> SymptomOptions.painOptions.find { it.value == entry.value }
                ?: SymptomOptions.painOptions.first()
            SymptomType.BLOATING -> createIntensityOption(entry.value, "Bloating")
            SymptomType.BREAST_TENDERNESS -> createIntensityOption(entry.value, "Breast Tenderness")
            SymptomType.ACNE -> createIntensityOption(entry.value, "Acne")
            SymptomType.ENERGY_LEVEL -> createEnergyOption(entry.value)
            SymptomType.SLEEP_QUALITY -> createSleepOption(entry.value)
            SymptomType.APPETITE -> createAppetiteOption(entry.value)
            SymptomType.NAUSEA -> createIntensityOption(entry.value, "Nausea")
            SymptomType.BACK_PAIN -> SymptomOptions.painOptions.find { it.value == entry.value }
                ?: SymptomOptions.painOptions.first()
            else -> createGenericOption(entry.value, entry.symptomType.name)
        }
    }
    
    private fun createIntensityOption(value: Int, label: String): SymptomOption {
        val intensityLabels = listOf("None", "Mild", "Moderate", "Severe")
        return SymptomOption(
            id = "${label.lowercase()}_$value",
            label = intensityLabels.getOrElse(value) { "Unknown" },
            emoji = when (value) {
                0 -> "😌"
                1 -> "😐"
                2 -> "😣"
                3 -> "😫"
                else -> "❓"
            },
            color = SymptomOptions.painOptions.getOrElse(value) { SymptomOptions.painOptions.first() }.color,
            value = value
        )
    }
    
    private fun createEnergyOption(value: Int): SymptomOption {
        val energyLabels = listOf("Very Low", "Low", "Normal", "High", "Very High")
        val energyEmojis = listOf("😴", "😑", "😊", "😄", "⚡")
        return SymptomOption(
            id = "energy_$value",
            label = energyLabels.getOrElse(value) { "Unknown" },
            emoji = energyEmojis.getOrElse(value) { "❓" },
            color = SymptomOptions.moodOptions.getOrElse(value % SymptomOptions.moodOptions.size) { SymptomOptions.moodOptions.first() }.color,
            value = value
        )
    }
    
    private fun createSleepOption(value: Int): SymptomOption {
        val sleepLabels = listOf("Very Poor", "Poor", "Fair", "Good", "Excellent")
        val sleepEmojis = listOf("😵", "😴", "😐", "😊", "😇")
        return SymptomOption(
            id = "sleep_$value",
            label = sleepLabels.getOrElse(value) { "Unknown" },
            emoji = sleepEmojis.getOrElse(value) { "❓" },
            color = SymptomOptions.moodOptions.getOrElse(value % SymptomOptions.moodOptions.size) { SymptomOptions.moodOptions.first() }.color,
            value = value
        )
    }
    
    private fun createAppetiteOption(value: Int): SymptomOption {
        val appetiteLabels = listOf("Very Low", "Low", "Normal", "High", "Very High")
        val appetiteEmojis = listOf("🚫", "😐", "😊", "😋", "🤤")
        return SymptomOption(
            id = "appetite_$value",
            label = appetiteLabels.getOrElse(value) { "Unknown" },
            emoji = appetiteEmojis.getOrElse(value) { "❓" },
            color = SymptomOptions.moodOptions.getOrElse(value % SymptomOptions.moodOptions.size) { SymptomOptions.moodOptions.first() }.color,
            value = value
        )
    }
    
    private fun createGenericOption(value: Int, typeName: String): SymptomOption {
        return SymptomOption(
            id = "${typeName.lowercase()}_$value",
            label = value.toString(),
            emoji = "📊",
            color = SymptomOptions.moodOptions.first().color,
            value = value
        )
    }
    
    private fun getAllAvailableSymptomTypes(): List<SymptomType> {
        return listOf(
            SymptomType.FLOW_INTENSITY,
            SymptomType.MOOD,
            SymptomType.PAIN_LEVEL,
            SymptomType.CRAMPS,
            SymptomType.HEADACHE,
            SymptomType.BLOATING,
            SymptomType.BREAST_TENDERNESS,
            SymptomType.ACNE,
            SymptomType.ENERGY_LEVEL,
            SymptomType.SLEEP_QUALITY,
            SymptomType.APPETITE,
            SymptomType.NAUSEA,
            SymptomType.BACK_PAIN
        )
    }
    
    fun selectDate(date: LocalDate) {
        viewModelScope.launch {
            val symptoms = cycleRepository.getSymptomEntriesByDate(date)
            val currentSymptoms = mapCurrentSymptoms(symptoms)
            
            _uiState.value = _uiState.value.copy(
                selectedDate = date,
                currentSymptoms = currentSymptoms
            )
        }
    }
    
    fun logSymptom(symptomType: SymptomType, option: SymptomOption) {
        viewModelScope.launch {
            cycleRepository.logSymptom(
                date = _uiState.value.selectedDate,
                symptomType = symptomType,
                value = option.value
            )
        }
    }
    
    fun removeSymptom(symptomType: SymptomType) {
        viewModelScope.launch {
            val symptomEntry = cycleRepository.getSymptomEntryByDateAndType(
                _uiState.value.selectedDate,
                symptomType
            )
            symptomEntry?.let {
                cycleRepository.deleteSymptomEntry(it)
            }
        }
    }
}

data class HealthLogsUiState(
    val isLoading: Boolean = true,
    val dailyLogs: List<DailyHealthLog> = emptyList(),
    val selectedDate: LocalDate = Clock.System.todayIn(TimeZone.currentSystemDefault()),
    val currentSymptoms: Map<SymptomType, SymptomOption?> = emptyMap(),
    val availableSymptomTypes: List<SymptomType> = emptyList(),
    val error: String? = null
)

data class DailyHealthLog(
    val date: LocalDate,
    val isPeriodDay: Boolean,
    val symptoms: List<SymptomEntry>,
    val hasData: Boolean
)
