package com.periodpal.app.notifications

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.os.Build
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationManagerCompat
import com.periodpal.app.MainActivity
import com.periodpal.app.R

class NotificationHelper(private val context: Context) {
    
    companion object {
        const val PERIOD_REMINDER_CHANNEL_ID = "period_reminder_channel"
        const val FERTILE_WINDOW_CHANNEL_ID = "fertile_window_channel"
        const val GENERAL_REMINDER_CHANNEL_ID = "general_reminder_channel"
        
        const val PERIOD_REMINDER_NOTIFICATION_ID = 1001
        const val FERTILE_WINDOW_NOTIFICATION_ID = 1002
        const val OVULATION_REMINDER_NOTIFICATION_ID = 1003
        const val SYMPTOM_REMINDER_NOTIFICATION_ID = 1004
    }
    
    init {
        createNotificationChannels()
    }
    
    private fun createNotificationChannels() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            
            // Period Reminder Channel
            val periodChannel = NotificationChannel(
                PERIOD_REMINDER_CHANNEL_ID,
                "Period Reminders",
                NotificationManager.IMPORTANCE_DEFAULT
            ).apply {
                description = "Notifications for upcoming periods"
                enableVibration(true)
                setShowBadge(true)
            }
            
            // Fertile Window Channel
            val fertileChannel = NotificationChannel(
                FERTILE_WINDOW_CHANNEL_ID,
                "Fertile Window",
                NotificationManager.IMPORTANCE_DEFAULT
            ).apply {
                description = "Notifications for fertile window and ovulation"
                enableVibration(true)
                setShowBadge(true)
            }
            
            // General Reminder Channel
            val generalChannel = NotificationChannel(
                GENERAL_REMINDER_CHANNEL_ID,
                "General Reminders",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "General app reminders and tips"
                setShowBadge(true)
            }
            
            notificationManager.createNotificationChannels(
                listOf(periodChannel, fertileChannel, generalChannel)
            )
        }
    }
    
    fun showPeriodReminderNotification(daysUntilPeriod: Int) {
        val intent = Intent(context, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        }
        
        val pendingIntent = PendingIntent.getActivity(
            context,
            0,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        val notification = NotificationCompat.Builder(context, PERIOD_REMINDER_CHANNEL_ID)
            .setSmallIcon(R.drawable.ic_period)
            .setContentTitle(context.getString(R.string.period_reminder_title))
            .setContentText(
                context.getString(R.string.period_reminder_message, daysUntilPeriod)
            )
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)
            .setCategory(NotificationCompat.CATEGORY_REMINDER)
            .build()
        
        with(NotificationManagerCompat.from(context)) {
            notify(PERIOD_REMINDER_NOTIFICATION_ID, notification)
        }
    }
    
    fun showFertileWindowNotification() {
        val intent = Intent(context, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        }
        
        val pendingIntent = PendingIntent.getActivity(
            context,
            0,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        val notification = NotificationCompat.Builder(context, FERTILE_WINDOW_CHANNEL_ID)
            .setSmallIcon(R.drawable.ic_ovulation)
            .setContentTitle(context.getString(R.string.fertile_window_title))
            .setContentText(context.getString(R.string.fertile_window_message))
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)
            .setCategory(NotificationCompat.CATEGORY_REMINDER)
            .build()
        
        with(NotificationManagerCompat.from(context)) {
            notify(FERTILE_WINDOW_NOTIFICATION_ID, notification)
        }
    }
    
    fun showOvulationReminderNotification() {
        val intent = Intent(context, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        }
        
        val pendingIntent = PendingIntent.getActivity(
            context,
            0,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        val notification = NotificationCompat.Builder(context, FERTILE_WINDOW_CHANNEL_ID)
            .setSmallIcon(R.drawable.ic_ovulation)
            .setContentTitle("Ovulation Day")
            .setContentText("Today is your predicted ovulation day")
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)
            .setCategory(NotificationCompat.CATEGORY_REMINDER)
            .build()
        
        with(NotificationManagerCompat.from(context)) {
            notify(OVULATION_REMINDER_NOTIFICATION_ID, notification)
        }
    }
    
    fun showSymptomReminderNotification() {
        val intent = Intent(context, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            putExtra("navigate_to", "health_logs")
        }
        
        val pendingIntent = PendingIntent.getActivity(
            context,
            0,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        val notification = NotificationCompat.Builder(context, GENERAL_REMINDER_CHANNEL_ID)
            .setSmallIcon(R.drawable.ic_launcher_foreground)
            .setContentTitle("Log Your Symptoms")
            .setContentText("Don't forget to log how you're feeling today")
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)
            .setCategory(NotificationCompat.CATEGORY_REMINDER)
            .build()
        
        with(NotificationManagerCompat.from(context)) {
            notify(SYMPTOM_REMINDER_NOTIFICATION_ID, notification)
        }
    }
    
    fun cancelNotification(notificationId: Int) {
        with(NotificationManagerCompat.from(context)) {
            cancel(notificationId)
        }
    }
    
    fun cancelAllNotifications() {
        with(NotificationManagerCompat.from(context)) {
            cancelAll()
        }
    }
    
    fun areNotificationsEnabled(): Boolean {
        return NotificationManagerCompat.from(context).areNotificationsEnabled()
    }
}
