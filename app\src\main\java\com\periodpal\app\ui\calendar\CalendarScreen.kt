package com.periodpal.app.ui.calendar

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ChevronLeft
import androidx.compose.material.icons.filled.ChevronRight
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.periodpal.app.ui.theme.FollicularGreen
import com.periodpal.app.ui.theme.LutealBlue
import com.periodpal.app.ui.theme.OvulationOrange
import com.periodpal.app.ui.theme.PeriodPalTheme
import com.periodpal.app.ui.theme.PeriodRed
import kotlinx.datetime.DateTimeUnit
import kotlinx.datetime.LocalDate
import kotlinx.datetime.Month
import kotlinx.datetime.minus
import kotlinx.datetime.plus
import java.time.format.TextStyle
import java.util.Locale

@Composable
fun CalendarScreen(
    modifier: Modifier = Modifier,
    viewModel: CalendarViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    
    CalendarContent(
        uiState = uiState,
        onDateSelected = viewModel::selectDate,
        onTogglePeriodDay = viewModel::togglePeriodDay,
        onNavigateToMonth = viewModel::navigateToMonth,
        modifier = modifier
    )
}

@Composable
fun CalendarContent(
    uiState: CalendarUiState,
    onDateSelected: (LocalDate) -> Unit,
    onTogglePeriodDay: (LocalDate) -> Unit,
    onNavigateToMonth: (LocalDate) -> Unit,
    modifier: Modifier = Modifier
) {
    if (uiState.isLoading) {
        Box(
            modifier = modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            CircularProgressIndicator(color = MaterialTheme.colorScheme.primary)
        }
        return
    }
    
    LazyColumn(
        modifier = modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        item {
            CalendarHeader(
                currentMonth = uiState.currentMonth,
                onNavigateToMonth = onNavigateToMonth
            )
        }
        
        item {
            CalendarGrid(
                calendarDays = uiState.calendarDays,
                currentMonth = uiState.currentMonth,
                selectedDate = uiState.selectedDate,
                onDateSelected = onDateSelected,
                onTogglePeriodDay = onTogglePeriodDay
            )
        }
        
        item {
            CalendarLegend()
        }
        
        uiState.selectedDate?.let { selectedDate ->
            item {
                SelectedDateDetails(
                    selectedDate = selectedDate,
                    calendarDay = uiState.calendarDays.find { it.date == selectedDate }
                )
            }
        }
    }
}

@Composable
fun CalendarHeader(
    currentMonth: LocalDate,
    onNavigateToMonth: (LocalDate) -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        IconButton(
            onClick = { onNavigateToMonth(currentMonth.minus(1, DateTimeUnit.MONTH)) }
        ) {
            Icon(
                imageVector = Icons.Default.ChevronLeft,
                contentDescription = "Previous month",
                tint = MaterialTheme.colorScheme.primary
            )
        }
        
        Text(
            text = "${currentMonth.month.getDisplayName(TextStyle.FULL, Locale.getDefault())} ${currentMonth.year}",
            style = MaterialTheme.typography.headlineSmall,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.onBackground
        )
        
        IconButton(
            onClick = { onNavigateToMonth(currentMonth.plus(1, DateTimeUnit.MONTH)) }
        ) {
            Icon(
                imageVector = Icons.Default.ChevronRight,
                contentDescription = "Next month",
                tint = MaterialTheme.colorScheme.primary
            )
        }
    }
}

@Composable
fun CalendarGrid(
    calendarDays: List<CalendarDay>,
    currentMonth: LocalDate,
    selectedDate: LocalDate?,
    onDateSelected: (LocalDate) -> Unit,
    onTogglePeriodDay: (LocalDate) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier) {
        // Day headers
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            val dayHeaders = listOf("Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat")
            dayHeaders.forEach { day ->
                Text(
                    text = day,
                    style = MaterialTheme.typography.labelMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.weight(1f)
                )
            }
        }
        
        Spacer(modifier = Modifier.height(8.dp))
        
        // Calendar days grid
        LazyVerticalGrid(
            columns = GridCells.Fixed(7),
            modifier = Modifier.height(300.dp),
            verticalArrangement = Arrangement.spacedBy(4.dp),
            horizontalArrangement = Arrangement.spacedBy(4.dp)
        ) {
            items(calendarDays.filter { 
                it.date.month == currentMonth.month && it.date.year == currentMonth.year 
            }) { calendarDay ->
                CalendarDayCell(
                    calendarDay = calendarDay,
                    isSelected = calendarDay.date == selectedDate,
                    onDateSelected = onDateSelected,
                    onTogglePeriodDay = onTogglePeriodDay
                )
            }
        }
    }
}

@Composable
fun CalendarDayCell(
    calendarDay: CalendarDay,
    isSelected: Boolean,
    onDateSelected: (LocalDate) -> Unit,
    onTogglePeriodDay: (LocalDate) -> Unit,
    modifier: Modifier = Modifier
) {
    val backgroundColor = when {
        calendarDay.isPeriodDay -> PeriodRed.copy(alpha = 0.3f)
        calendarDay.isOvulationDay -> OvulationOrange.copy(alpha = 0.3f)
        calendarDay.predictions.any { it.type == PredictionType.FERTILE } -> FollicularGreen.copy(alpha = 0.2f)
        calendarDay.predictions.any { it.type == PredictionType.PMS } -> LutealBlue.copy(alpha = 0.2f)
        else -> Color.Transparent
    }
    
    val borderColor = when {
        isSelected -> MaterialTheme.colorScheme.primary
        calendarDay.isToday -> MaterialTheme.colorScheme.secondary
        else -> Color.Transparent
    }
    
    Box(
        modifier = modifier
            .aspectRatio(1f)
            .clip(RoundedCornerShape(8.dp))
            .background(backgroundColor)
            .border(
                width = if (isSelected || calendarDay.isToday) 2.dp else 0.dp,
                color = borderColor,
                shape = RoundedCornerShape(8.dp)
            )
            .clickable { onDateSelected(calendarDay.date) },
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = calendarDay.date.dayOfMonth.toString(),
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = if (calendarDay.isToday) FontWeight.Bold else FontWeight.Normal,
                color = when {
                    calendarDay.isPeriodDay -> PeriodRed
                    calendarDay.isOvulationDay -> OvulationOrange
                    calendarDay.isToday -> MaterialTheme.colorScheme.primary
                    else -> MaterialTheme.colorScheme.onSurface
                }
            )
            
            // Symptom indicators
            if (calendarDay.symptoms.isNotEmpty()) {
                Row(
                    horizontalArrangement = Arrangement.spacedBy(1.dp)
                ) {
                    repeat(minOf(calendarDay.symptoms.size, 3)) {
                        Box(
                            modifier = Modifier
                                .size(3.dp)
                                .clip(CircleShape)
                                .background(MaterialTheme.colorScheme.primary)
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun CalendarLegend(
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Text(
                text = "Legend",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.SemiBold
            )
            
            Row(
                horizontalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                LegendItem(
                    color = PeriodRed,
                    label = "Period",
                    modifier = Modifier.weight(1f)
                )
                LegendItem(
                    color = OvulationOrange,
                    label = "Ovulation",
                    modifier = Modifier.weight(1f)
                )
            }
            
            Row(
                horizontalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                LegendItem(
                    color = FollicularGreen,
                    label = "Fertile Window",
                    modifier = Modifier.weight(1f)
                )
                LegendItem(
                    color = LutealBlue,
                    label = "PMS",
                    modifier = Modifier.weight(1f)
                )
            }
        }
    }
}

@Composable
fun LegendItem(
    color: Color,
    label: String,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        Box(
            modifier = Modifier
                .size(12.dp)
                .clip(CircleShape)
                .background(color.copy(alpha = 0.3f))
        )
        Text(
            text = label,
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

@Composable
fun SelectedDateDetails(
    selectedDate: LocalDate,
    calendarDay: CalendarDay?,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Text(
                text = selectedDate.toString(),
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.SemiBold
            )
            
            calendarDay?.let { day ->
                if (day.isPeriodDay) {
                    Text(
                        text = "Period Day",
                        style = MaterialTheme.typography.bodyMedium,
                        color = PeriodRed
                    )
                }
                
                if (day.isOvulationDay) {
                    Text(
                        text = "Ovulation Day",
                        style = MaterialTheme.typography.bodyMedium,
                        color = OvulationOrange
                    )
                }
                
                if (day.symptoms.isNotEmpty()) {
                    Text(
                        text = "Symptoms logged: ${day.symptoms.size}",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
                
                if (day.predictions.isNotEmpty()) {
                    Text(
                        text = "Predictions: ${day.predictions.joinToString { it.type.name.lowercase() }}",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun CalendarScreenPreview() {
    PeriodPalTheme {
        CalendarContent(
            uiState = CalendarUiState(
                isLoading = false,
                currentMonth = LocalDate(2024, 1, 15),
                calendarDays = emptyList()
            ),
            onDateSelected = { },
            onTogglePeriodDay = { },
            onNavigateToMonth = { }
        )
    }
}
