package com.periodpal.app.ui.settings

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.ChevronRight
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material.icons.filled.Notifications
import androidx.compose.material.icons.filled.Palette
import androidx.compose.material.icons.filled.Person
import androidx.compose.material.icons.filled.Phone
import androidx.compose.material.icons.filled.Security
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Divider
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Slider
import androidx.compose.material3.Switch
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.periodpal.app.data.entities.EmergencyContact
import com.periodpal.app.data.entities.UserPreferences
import com.periodpal.app.ui.theme.PeriodPalTheme
import kotlin.math.roundToInt

@Composable
fun SettingsScreen(
    modifier: Modifier = Modifier,
    viewModel: SettingsViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    
    SettingsContent(
        uiState = uiState,
        onCycleLengthChanged = viewModel::updateCycleLength,
        onPeriodLengthChanged = viewModel::updatePeriodLength,
        onNotificationsToggled = viewModel::updateNotificationsEnabled,
        onPeriodReminderDaysChanged = viewModel::updatePeriodReminderDays,
        onFertileWindowReminderToggled = viewModel::updateFertileWindowReminder,
        onThemeChanged = viewModel::updateTheme,
        onPrivacyModeToggled = viewModel::updatePrivacyMode,
        onAddEmergencyContact = viewModel::showAddContactDialog,
        onEditEmergencyContact = viewModel::showEditContactDialog,
        onDeleteEmergencyContact = viewModel::deleteEmergencyContact,
        modifier = modifier
    )
}

@Composable
fun SettingsContent(
    uiState: SettingsUiState,
    onCycleLengthChanged: (Int) -> Unit,
    onPeriodLengthChanged: (Int) -> Unit,
    onNotificationsToggled: (Boolean) -> Unit,
    onPeriodReminderDaysChanged: (Int) -> Unit,
    onFertileWindowReminderToggled: (Boolean) -> Unit,
    onThemeChanged: (String) -> Unit,
    onPrivacyModeToggled: (Boolean) -> Unit,
    onAddEmergencyContact: () -> Unit,
    onEditEmergencyContact: (EmergencyContact) -> Unit,
    onDeleteEmergencyContact: (EmergencyContact) -> Unit,
    modifier: Modifier = Modifier
) {
    if (uiState.isLoading) {
        Box(
            modifier = modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            CircularProgressIndicator(color = MaterialTheme.colorScheme.primary)
        }
        return
    }
    
    val userPrefs = uiState.userPreferences ?: return
    
    Scaffold(
        floatingActionButton = {
            FloatingActionButton(
                onClick = onAddEmergencyContact,
                containerColor = MaterialTheme.colorScheme.primary
            ) {
                Icon(
                    imageVector = Icons.Default.Add,
                    contentDescription = "Add emergency contact",
                    tint = MaterialTheme.colorScheme.onPrimary
                )
            }
        }
    ) { paddingValues ->
        LazyColumn(
            modifier = modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            item {
                Text(
                    text = "Settings",
                    style = MaterialTheme.typography.headlineMedium,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onBackground
                )
            }
            
            item {
                CycleSettingsCard(
                    userPreferences = userPrefs,
                    onCycleLengthChanged = onCycleLengthChanged,
                    onPeriodLengthChanged = onPeriodLengthChanged
                )
            }
            
            item {
                NotificationSettingsCard(
                    userPreferences = userPrefs,
                    onNotificationsToggled = onNotificationsToggled,
                    onPeriodReminderDaysChanged = onPeriodReminderDaysChanged,
                    onFertileWindowReminderToggled = onFertileWindowReminderToggled
                )
            }
            
            item {
                AppearanceSettingsCard(
                    userPreferences = userPrefs,
                    onThemeChanged = onThemeChanged,
                    onPrivacyModeToggled = onPrivacyModeToggled
                )
            }
            
            item {
                Text(
                    text = "Emergency Contacts",
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.SemiBold,
                    color = MaterialTheme.colorScheme.onBackground
                )
            }
            
            items(uiState.emergencyContacts) { contact ->
                EmergencyContactCard(
                    contact = contact,
                    onEdit = { onEditEmergencyContact(contact) },
                    onDelete = { onDeleteEmergencyContact(contact) }
                )
            }
            
            if (uiState.emergencyContacts.isEmpty()) {
                item {
                    Card(
                        modifier = Modifier.fillMaxWidth(),
                        colors = CardDefaults.cardColors(
                            containerColor = MaterialTheme.colorScheme.surfaceVariant
                        )
                    ) {
                        Column(
                            modifier = Modifier.padding(16.dp),
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            Icon(
                                imageVector = Icons.Default.Phone,
                                contentDescription = null,
                                tint = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                            Spacer(modifier = Modifier.height(8.dp))
                            Text(
                                text = "No emergency contacts added",
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                            Text(
                                text = "Tap + to add your first contact",
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    }
                }
            }
            
            item {
                Spacer(modifier = Modifier.height(80.dp))
            }
        }
    }
}

@Composable
fun CycleSettingsCard(
    userPreferences: UserPreferences,
    onCycleLengthChanged: (Int) -> Unit,
    onPeriodLengthChanged: (Int) -> Unit,
    modifier: Modifier = Modifier
) {
    SettingsCard(
        title = "Cycle Settings",
        icon = Icons.Default.Person,
        modifier = modifier
    ) {
        var cycleLength by remember { mutableStateOf(userPreferences.averageCycleLength.toFloat()) }
        var periodLength by remember { mutableStateOf(userPreferences.averagePeriodLength.toFloat()) }
        
        SliderSetting(
            label = "Average Cycle Length",
            value = cycleLength,
            valueRange = 21f..35f,
            onValueChange = { 
                cycleLength = it
                onCycleLengthChanged(it.roundToInt())
            },
            valueText = "${cycleLength.roundToInt()} days"
        )
        
        SliderSetting(
            label = "Average Period Length",
            value = periodLength,
            valueRange = 3f..8f,
            onValueChange = { 
                periodLength = it
                onPeriodLengthChanged(it.roundToInt())
            },
            valueText = "${periodLength.roundToInt()} days"
        )
    }
}

@Composable
fun NotificationSettingsCard(
    userPreferences: UserPreferences,
    onNotificationsToggled: (Boolean) -> Unit,
    onPeriodReminderDaysChanged: (Int) -> Unit,
    onFertileWindowReminderToggled: (Boolean) -> Unit,
    modifier: Modifier = Modifier
) {
    SettingsCard(
        title = "Notifications",
        icon = Icons.Default.Notifications,
        modifier = modifier
    ) {
        SwitchSetting(
            label = "Enable Notifications",
            checked = userPreferences.notificationsEnabled,
            onCheckedChange = onNotificationsToggled
        )
        
        if (userPreferences.notificationsEnabled) {
            var reminderDays by remember { mutableStateOf(userPreferences.periodReminderDays.toFloat()) }
            
            SliderSetting(
                label = "Period Reminder",
                value = reminderDays,
                valueRange = 1f..7f,
                onValueChange = { 
                    reminderDays = it
                    onPeriodReminderDaysChanged(it.roundToInt())
                },
                valueText = "${reminderDays.roundToInt()} days before"
            )
            
            SwitchSetting(
                label = "Fertile Window Reminder",
                checked = userPreferences.fertileWindowReminder,
                onCheckedChange = onFertileWindowReminderToggled
            )
        }
    }
}

@Composable
fun AppearanceSettingsCard(
    userPreferences: UserPreferences,
    onThemeChanged: (String) -> Unit,
    onPrivacyModeToggled: (Boolean) -> Unit,
    modifier: Modifier = Modifier
) {
    SettingsCard(
        title = "Appearance & Privacy",
        icon = Icons.Default.Palette,
        modifier = modifier
    ) {
        ClickableSetting(
            label = "Theme",
            value = userPreferences.theme,
            onClick = { /* TODO: Show theme picker */ }
        )
        
        SwitchSetting(
            label = "Privacy Mode",
            description = "Hide sensitive information on lock screen",
            checked = userPreferences.privacyMode,
            onCheckedChange = onPrivacyModeToggled
        )
    }
}

@Composable
fun SettingsCard(
    title: String,
    icon: ImageVector,
    modifier: Modifier = Modifier,
    content: @Composable () -> Unit
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Icon(
                    imageVector = icon,
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.primary
                )
                Text(
                    text = title,
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.SemiBold
                )
            }
            
            content()
        }
    }
}

@Composable
fun SliderSetting(
    label: String,
    value: Float,
    valueRange: ClosedFloatingPointRange<Float>,
    onValueChange: (Float) -> Unit,
    valueText: String,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = label,
                style = MaterialTheme.typography.bodyMedium
            )
            Text(
                text = valueText,
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.SemiBold,
                color = MaterialTheme.colorScheme.primary
            )
        }
        
        Slider(
            value = value,
            onValueChange = onValueChange,
            valueRange = valueRange,
            modifier = Modifier.fillMaxWidth()
        )
    }
}

@Composable
fun SwitchSetting(
    label: String,
    checked: Boolean,
    onCheckedChange: (Boolean) -> Unit,
    description: String? = null,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Column(modifier = Modifier.weight(1f)) {
            Text(
                text = label,
                style = MaterialTheme.typography.bodyMedium
            )
            description?.let { desc ->
                Text(
                    text = desc,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
        
        Switch(
            checked = checked,
            onCheckedChange = onCheckedChange
        )
    }
}

@Composable
fun ClickableSetting(
    label: String,
    value: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .clickable { onClick() },
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = label,
            style = MaterialTheme.typography.bodyMedium
        )
        
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(4.dp)
        ) {
            Text(
                text = value,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            Icon(
                imageVector = Icons.Default.ChevronRight,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

@Composable
fun EmergencyContactCard(
    contact: EmergencyContact,
    onEdit: () -> Unit,
    onDelete: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = contact.name,
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.SemiBold
                )
                Text(
                    text = contact.phoneNumber,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                Text(
                    text = contact.relationship,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
            
            Row {
                IconButton(onClick = onEdit) {
                    Icon(
                        imageVector = Icons.Default.Edit,
                        contentDescription = "Edit contact",
                        tint = MaterialTheme.colorScheme.primary
                    )
                }
                IconButton(onClick = onDelete) {
                    Icon(
                        imageVector = Icons.Default.Delete,
                        contentDescription = "Delete contact",
                        tint = MaterialTheme.colorScheme.error
                    )
                }
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun SettingsScreenPreview() {
    PeriodPalTheme {
        SettingsContent(
            uiState = SettingsUiState(
                isLoading = false,
                userPreferences = UserPreferences(),
                emergencyContacts = emptyList()
            ),
            onCycleLengthChanged = { },
            onPeriodLengthChanged = { },
            onNotificationsToggled = { },
            onPeriodReminderDaysChanged = { },
            onFertileWindowReminderToggled = { },
            onThemeChanged = { },
            onPrivacyModeToggled = { },
            onAddEmergencyContact = { },
            onEditEmergencyContact = { },
            onDeleteEmergencyContact = { }
        )
    }
}
