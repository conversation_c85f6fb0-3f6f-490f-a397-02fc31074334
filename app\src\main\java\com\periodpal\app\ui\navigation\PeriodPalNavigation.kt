package com.periodpal.app.ui.navigation

import androidx.compose.foundation.layout.padding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Analytics
import androidx.compose.material.icons.filled.CalendarMonth
import androidx.compose.material.icons.filled.Dashboard
import androidx.compose.material.icons.filled.HealthAndSafety
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.NavigationBar
import androidx.compose.material3.NavigationBarItem
import androidx.compose.material3.NavigationBarItemDefaults
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.stringResource
import androidx.navigation.NavController
import androidx.navigation.NavDestination.Companion.hierarchy
import androidx.navigation.NavGraph.Companion.findStartDestination
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.currentBackStackEntryAsState
import androidx.navigation.compose.rememberNavController
import com.periodpal.app.R
import com.periodpal.app.ui.dashboard.DashboardScreen
import com.periodpal.app.ui.calendar.CalendarScreen
import com.periodpal.app.ui.healthlogs.HealthLogsScreen
import com.periodpal.app.ui.insights.InsightsScreen
import com.periodpal.app.ui.settings.SettingsScreen

@Composable
fun PeriodPalNavigation(
    navController: NavController = rememberNavController()
) {
    Scaffold(
        bottomBar = {
            PeriodPalBottomNavigation(navController = navController)
        }
    ) { innerPadding ->
        NavHost(
            navController = navController,
            startDestination = Screen.Dashboard.route,
            modifier = Modifier.padding(innerPadding)
        ) {
            composable(Screen.Dashboard.route) {
                DashboardScreen()
            }
            composable(Screen.Calendar.route) {
                CalendarScreen()
            }
            composable(Screen.HealthLogs.route) {
                HealthLogsScreen()
            }
            composable(Screen.Insights.route) {
                InsightsScreen()
            }
            composable(Screen.Settings.route) {
                SettingsScreen()
            }
        }
    }
}

@Composable
fun PeriodPalBottomNavigation(
    navController: NavController
) {
    val screens = listOf(
        Screen.Dashboard,
        Screen.Calendar,
        Screen.HealthLogs,
        Screen.Insights,
        Screen.Settings
    )
    
    val navBackStackEntry by navController.currentBackStackEntryAsState()
    val currentDestination = navBackStackEntry?.destination
    
    NavigationBar(
        containerColor = MaterialTheme.colorScheme.surface,
        contentColor = MaterialTheme.colorScheme.onSurface
    ) {
        screens.forEach { screen ->
            NavigationBarItem(
                icon = {
                    Icon(
                        imageVector = screen.icon,
                        contentDescription = stringResource(screen.resourceId)
                    )
                },
                label = {
                    Text(
                        text = stringResource(screen.resourceId),
                        style = MaterialTheme.typography.labelSmall
                    )
                },
                selected = currentDestination?.hierarchy?.any { it.route == screen.route } == true,
                onClick = {
                    navController.navigate(screen.route) {
                        // Pop up to the start destination of the graph to
                        // avoid building up a large stack of destinations
                        // on the back stack as users select items
                        popUpTo(navController.graph.findStartDestination().id) {
                            saveState = true
                        }
                        // Avoid multiple copies of the same destination when
                        // reselecting the same item
                        launchSingleTop = true
                        // Restore state when reselecting a previously selected item
                        restoreState = true
                    }
                },
                colors = NavigationBarItemDefaults.colors(
                    selectedIconColor = MaterialTheme.colorScheme.primary,
                    selectedTextColor = MaterialTheme.colorScheme.primary,
                    unselectedIconColor = MaterialTheme.colorScheme.onSurfaceVariant,
                    unselectedTextColor = MaterialTheme.colorScheme.onSurfaceVariant,
                    indicatorColor = MaterialTheme.colorScheme.primaryContainer
                )
            )
        }
    }
}

sealed class Screen(
    val route: String,
    val resourceId: Int,
    val icon: ImageVector
) {
    object Dashboard : Screen("dashboard", R.string.nav_dashboard, Icons.Default.Dashboard)
    object Calendar : Screen("calendar", R.string.nav_calendar, Icons.Default.CalendarMonth)
    object HealthLogs : Screen("health_logs", R.string.nav_health_logs, Icons.Default.HealthAndSafety)
    object Insights : Screen("insights", R.string.nav_insights, Icons.Default.Analytics)
    object Settings : Screen("settings", R.string.nav_settings, Icons.Default.Settings)
}

// Placeholder screens for navigation structure
@Composable
fun CalendarPlaceholderScreen() {
    androidx.compose.foundation.layout.Box(
        modifier = Modifier.padding(16.dp),
        contentAlignment = androidx.compose.ui.Alignment.Center
    ) {
        Text(
            text = "Calendar Screen - Coming Soon",
            style = MaterialTheme.typography.headlineMedium
        )
    }
}

@Composable
fun HealthLogsPlaceholderScreen() {
    androidx.compose.foundation.layout.Box(
        modifier = Modifier.padding(16.dp),
        contentAlignment = androidx.compose.ui.Alignment.Center
    ) {
        Text(
            text = "Health Logs Screen - Coming Soon",
            style = MaterialTheme.typography.headlineMedium
        )
    }
}

@Composable
fun InsightsPlaceholderScreen() {
    androidx.compose.foundation.layout.Box(
        modifier = Modifier.padding(16.dp),
        contentAlignment = androidx.compose.ui.Alignment.Center
    ) {
        Text(
            text = "Insights Screen - Coming Soon",
            style = MaterialTheme.typography.headlineMedium
        )
    }
}

@Composable
fun SettingsPlaceholderScreen() {
    androidx.compose.foundation.layout.Box(
        modifier = Modifier.padding(16.dp),
        contentAlignment = androidx.compose.ui.Alignment.Center
    ) {
        Text(
            text = "Settings Screen - Coming Soon",
            style = MaterialTheme.typography.headlineMedium
        )
    }
}
