package com.periodpal.app.ui.healthlogs

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Face
import androidx.compose.material.icons.filled.Favorite
import androidx.compose.material.icons.filled.LocalHospital
import androidx.compose.material.icons.filled.Restaurant
import androidx.compose.material.icons.filled.Sleep
import androidx.compose.material.icons.filled.SportsGymnastics
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.periodpal.app.data.entities.SymptomType
import com.periodpal.app.ui.components.SymptomCard
import com.periodpal.app.ui.components.SymptomOptions
import com.periodpal.app.ui.theme.PeriodPalTheme
import com.periodpal.app.ui.theme.PeriodRed
import kotlinx.datetime.LocalDate

@Composable
fun HealthLogsScreen(
    modifier: Modifier = Modifier,
    viewModel: HealthLogsViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    
    HealthLogsContent(
        uiState = uiState,
        onDateSelected = viewModel::selectDate,
        onSymptomLogged = viewModel::logSymptom,
        onSymptomRemoved = viewModel::removeSymptom,
        modifier = modifier
    )
}

@Composable
fun HealthLogsContent(
    uiState: HealthLogsUiState,
    onDateSelected: (LocalDate) -> Unit,
    onSymptomLogged: (SymptomType, com.periodpal.app.ui.components.SymptomOption) -> Unit,
    onSymptomRemoved: (SymptomType) -> Unit,
    modifier: Modifier = Modifier
) {
    if (uiState.isLoading) {
        Box(
            modifier = modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            CircularProgressIndicator(color = MaterialTheme.colorScheme.primary)
        }
        return
    }
    
    Scaffold(
        floatingActionButton = {
            FloatingActionButton(
                onClick = { /* TODO: Add custom symptom */ },
                containerColor = MaterialTheme.colorScheme.primary
            ) {
                Icon(
                    imageVector = Icons.Default.Add,
                    contentDescription = "Add custom symptom",
                    tint = MaterialTheme.colorScheme.onPrimary
                )
            }
        }
    ) { paddingValues ->
        LazyColumn(
            modifier = modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            item {
                Text(
                    text = "Health Logs",
                    style = MaterialTheme.typography.headlineMedium,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onBackground
                )
            }
            
            item {
                QuickSymptomLogging(
                    selectedDate = uiState.selectedDate,
                    currentSymptoms = uiState.currentSymptoms,
                    onSymptomLogged = onSymptomLogged
                )
            }
            
            item {
                Text(
                    text = "Recent Logs",
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.SemiBold,
                    color = MaterialTheme.colorScheme.onBackground
                )
            }
            
            items(uiState.dailyLogs) { dailyLog ->
                DailyLogCard(
                    dailyLog = dailyLog,
                    isSelected = dailyLog.date == uiState.selectedDate,
                    onDateSelected = onDateSelected
                )
            }
            
            item {
                Spacer(modifier = Modifier.height(80.dp))
            }
        }
    }
}

@Composable
fun QuickSymptomLogging(
    selectedDate: LocalDate,
    currentSymptoms: Map<SymptomType, com.periodpal.app.ui.components.SymptomOption?>,
    onSymptomLogged: (SymptomType, com.periodpal.app.ui.components.SymptomOption) -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            Text(
                text = "Log for ${selectedDate}",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.SemiBold
            )
            
            // Flow Intensity (only show if period day or already logged)
            val flowSymptom = currentSymptoms[SymptomType.FLOW_INTENSITY]
            if (flowSymptom != null) {
                SymptomCard(
                    title = "Flow Intensity",
                    icon = Icons.Default.Favorite,
                    options = SymptomOptions.flowOptions,
                    selectedOption = flowSymptom,
                    onOptionSelected = { option ->
                        onSymptomLogged(SymptomType.FLOW_INTENSITY, option)
                    }
                )
            }
            
            // Mood
            SymptomCard(
                title = "Mood",
                icon = Icons.Default.Face,
                options = SymptomOptions.moodOptions,
                selectedOption = currentSymptoms[SymptomType.MOOD],
                onOptionSelected = { option ->
                    onSymptomLogged(SymptomType.MOOD, option)
                }
            )
            
            // Pain Level
            SymptomCard(
                title = "Pain Level",
                icon = Icons.Default.LocalHospital,
                options = SymptomOptions.painOptions,
                selectedOption = currentSymptoms[SymptomType.PAIN_LEVEL],
                onOptionSelected = { option ->
                    onSymptomLogged(SymptomType.PAIN_LEVEL, option)
                }
            )
            
            // Energy Level
            SymptomCard(
                title = "Energy Level",
                icon = Icons.Default.SportsGymnastics,
                options = SymptomOptions.energyOptions,
                selectedOption = currentSymptoms[SymptomType.ENERGY_LEVEL],
                onOptionSelected = { option ->
                    onSymptomLogged(SymptomType.ENERGY_LEVEL, option)
                }
            )
            
            // Sleep Quality
            SymptomCard(
                title = "Sleep Quality",
                icon = Icons.Default.Sleep,
                options = SymptomOptions.sleepOptions,
                selectedOption = currentSymptoms[SymptomType.SLEEP_QUALITY],
                onOptionSelected = { option ->
                    onSymptomLogged(SymptomType.SLEEP_QUALITY, option)
                }
            )
            
            // Appetite
            SymptomCard(
                title = "Appetite",
                icon = Icons.Default.Restaurant,
                options = SymptomOptions.appetiteOptions,
                selectedOption = currentSymptoms[SymptomType.APPETITE],
                onOptionSelected = { option ->
                    onSymptomLogged(SymptomType.APPETITE, option)
                }
            )
        }
    }
}

@Composable
fun DailyLogCard(
    dailyLog: DailyHealthLog,
    isSelected: Boolean,
    onDateSelected: (LocalDate) -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .clickable { onDateSelected(dailyLog.date) },
        colors = CardDefaults.cardColors(
            containerColor = if (isSelected) {
                MaterialTheme.colorScheme.primaryContainer
            } else {
                MaterialTheme.colorScheme.surface
            }
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = if (isSelected) 4.dp else 2.dp
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column {
                Text(
                    text = dailyLog.date.toString(),
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.SemiBold,
                    color = if (isSelected) {
                        MaterialTheme.colorScheme.onPrimaryContainer
                    } else {
                        MaterialTheme.colorScheme.onSurface
                    }
                )
                
                if (dailyLog.isPeriodDay) {
                    Text(
                        text = "Period Day",
                        style = MaterialTheme.typography.bodySmall,
                        color = PeriodRed
                    )
                }
                
                if (dailyLog.symptoms.isNotEmpty()) {
                    Text(
                        text = "${dailyLog.symptoms.size} symptoms logged",
                        style = MaterialTheme.typography.bodySmall,
                        color = if (isSelected) {
                            MaterialTheme.colorScheme.onPrimaryContainer
                        } else {
                            MaterialTheme.colorScheme.onSurfaceVariant
                        }
                    )
                }
            }
            
            // Symptom indicators
            if (dailyLog.symptoms.isNotEmpty()) {
                LazyRow(
                    horizontalArrangement = Arrangement.spacedBy(4.dp)
                ) {
                    items(dailyLog.symptoms.take(5)) { symptom ->
                        Box(
                            modifier = Modifier
                                .size(8.dp)
                                .clip(CircleShape)
                                .background(getSymptomColor(symptom.symptomType))
                        )
                    }
                    
                    if (dailyLog.symptoms.size > 5) {
                        item {
                            Text(
                                text = "+${dailyLog.symptoms.size - 5}",
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun getSymptomColor(symptomType: SymptomType): Color {
    return when (symptomType) {
        SymptomType.FLOW_INTENSITY -> PeriodRed
        SymptomType.MOOD -> com.periodpal.app.ui.theme.MoodHappy
        SymptomType.PAIN_LEVEL -> com.periodpal.app.ui.theme.PainModerate
        SymptomType.ENERGY_LEVEL -> com.periodpal.app.ui.theme.FollicularGreen
        SymptomType.SLEEP_QUALITY -> com.periodpal.app.ui.theme.LutealBlue
        SymptomType.APPETITE -> com.periodpal.app.ui.theme.OvulationOrange
        else -> MaterialTheme.colorScheme.primary
    }
}

@Preview(showBackground = true)
@Composable
fun HealthLogsScreenPreview() {
    PeriodPalTheme {
        HealthLogsContent(
            uiState = HealthLogsUiState(
                isLoading = false,
                dailyLogs = emptyList(),
                selectedDate = LocalDate(2024, 1, 15),
                currentSymptoms = emptyMap()
            ),
            onDateSelected = { },
            onSymptomLogged = { _, _ -> },
            onSymptomRemoved = { }
        )
    }
}
