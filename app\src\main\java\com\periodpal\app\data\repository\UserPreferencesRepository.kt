package com.periodpal.app.data.repository

import com.periodpal.app.data.dao.UserPreferencesDao
import com.periodpal.app.data.entities.UserPreferences
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.first
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class UserPreferencesRepository @Inject constructor(
    private val userPreferencesDao: UserPreferencesDao
) {
    
    fun getUserPreferences(): Flow<UserPreferences?> = userPreferencesDao.getUserPreferences()
    
    suspend fun getUserPreferencesOnce(): UserPreferences {
        return userPreferencesDao.getUserPreferencesOnce() ?: run {
            // Create default preferences if none exist
            val defaultPreferences = UserPreferences()
            userPreferencesDao.insertUserPreferences(defaultPreferences)
            defaultPreferences
        }
    }
    
    suspend fun updateUserPreferences(userPreferences: UserPreferences) {
        val updatedPreferences = userPreferences.copy(
            updatedAt = kotlinx.datetime.Clock.System.now()
        )
        userPreferencesDao.updateUserPreferences(updatedPreferences)
    }
    
    suspend fun updateAverageCycleLength(cycleLength: Int) {
        userPreferencesDao.updateAverageCycleLength(cycleLength)
    }
    
    suspend fun updateAveragePeriodLength(periodLength: Int) {
        userPreferencesDao.updateAveragePeriodLength(periodLength)
    }
    
    suspend fun updateNotificationsEnabled(enabled: Boolean) {
        userPreferencesDao.updateNotificationsEnabled(enabled)
    }
    
    suspend fun updatePeriodReminderDays(days: Int) {
        userPreferencesDao.updatePeriodReminderDays(days)
    }
    
    suspend fun updateFertileWindowReminder(enabled: Boolean) {
        userPreferencesDao.updateFertileWindowReminder(enabled)
    }
    
    suspend fun updateTheme(theme: String) {
        userPreferencesDao.updateTheme(theme)
    }
    
    suspend fun updatePrivacyMode(enabled: Boolean) {
        userPreferencesDao.updatePrivacyMode(enabled)
    }
}
