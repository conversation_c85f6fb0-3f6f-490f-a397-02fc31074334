package com.periodpal.app.ui.insights

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.periodpal.app.data.entities.SymptomType
import com.periodpal.app.data.repository.CycleRepository
import com.periodpal.app.data.repository.UserPreferencesRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.launch
import kotlinx.datetime.Clock
import kotlinx.datetime.DateTimeUnit
import kotlinx.datetime.LocalDate
import kotlinx.datetime.TimeZone
import kotlinx.datetime.minus
import kotlinx.datetime.todayIn
import javax.inject.Inject

@HiltViewModel
class InsightsViewModel @Inject constructor(
    private val cycleRepository: CycleRepository,
    private val userPreferencesRepository: UserPreferencesRepository
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(InsightsUiState())
    val uiState: StateFlow<InsightsUiState> = _uiState.asStateFlow()
    
    private val today = Clock.System.todayIn(TimeZone.currentSystemDefault())
    
    init {
        loadInsights()
    }
    
    private fun loadInsights() {
        viewModelScope.launch {
            combine(
                cycleRepository.getAllCycleEntries(),
                cycleRepository.getAllSymptomEntries(),
                userPreferencesRepository.getUserPreferences()
            ) { cycleEntries, symptomEntries, preferences ->
                val userPrefs = preferences ?: return@combine
                
                // Calculate cycle statistics
                val periodDays = cycleEntries.filter { it.isPeriodDay }
                val cycleStats = calculateCycleStatistics(periodDays, userPrefs)
                
                // Calculate symptom trends (last 3 months)
                val threeMonthsAgo = today.minus(3, DateTimeUnit.MONTH)
                val recentSymptoms = symptomEntries.filter { it.date >= threeMonthsAgo }
                val symptomTrends = calculateSymptomTrends(recentSymptoms)
                
                // Calculate predictions
                val lastPeriodDay = cycleRepository.getLastPeriodDay()
                val predictions = calculatePredictions(lastPeriodDay, userPrefs)
                
                // Calculate cycle regularity
                val cycleRegularity = calculateCycleRegularity(periodDays)
                
                // Calculate most common symptoms
                val commonSymptoms = calculateMostCommonSymptoms(recentSymptoms)
                
                InsightsUiState(
                    isLoading = false,
                    cycleStatistics = cycleStats,
                    symptomTrends = symptomTrends,
                    predictions = predictions,
                    cycleRegularity = cycleRegularity,
                    commonSymptoms = commonSymptoms,
                    averageCycleLength = cycleStats.averageCycleLength,
                    averagePeriodLength = cycleStats.averagePeriodLength
                )
            }.collect { newState ->
                _uiState.value = newState
            }
        }
    }
    
    private suspend fun calculateCycleStatistics(
        periodDays: List<com.periodpal.app.data.entities.CycleEntry>,
        userPrefs: com.periodpal.app.data.entities.UserPreferences
    ): CycleStatistics {
        val sortedPeriodDays = periodDays.sortedBy { it.date }
        
        if (sortedPeriodDays.size < 2) {
            return CycleStatistics(
                averageCycleLength = userPrefs.averageCycleLength.toDouble(),
                averagePeriodLength = userPrefs.averagePeriodLength.toDouble(),
                totalCycles = 0,
                shortestCycle = userPrefs.averageCycleLength,
                longestCycle = userPrefs.averageCycleLength,
                cycleLengths = emptyList()
            )
        }
        
        // Group consecutive period days to find cycle starts
        val cycleStarts = mutableListOf<LocalDate>()
        var currentCycleStart: LocalDate? = null
        
        for (i in sortedPeriodDays.indices) {
            val currentDay = sortedPeriodDays[i]
            val previousDay = if (i > 0) sortedPeriodDays[i - 1] else null
            
            // If this is the first period day or there's a gap, it's a new cycle
            if (previousDay == null || 
                currentDay.date.toEpochDays() - previousDay.date.toEpochDays() > 1) {
                currentCycleStart = currentDay.date
                cycleStarts.add(currentDay.date)
            }
        }
        
        // Calculate cycle lengths
        val cycleLengths = mutableListOf<Int>()
        for (i in 0 until cycleStarts.size - 1) {
            val cycleLength = (cycleStarts[i + 1].toEpochDays() - cycleStarts[i].toEpochDays()).toInt()
            if (cycleLength in 15..45) { // Only include reasonable cycle lengths
                cycleLengths.add(cycleLength)
            }
        }
        
        val averageCycleLength = if (cycleLengths.isNotEmpty()) {
            cycleLengths.average()
        } else {
            userPrefs.averageCycleLength.toDouble()
        }
        
        // Calculate average period length
        val periodLengths = mutableListOf<Int>()
        var currentPeriodLength = 0
        var lastDate: LocalDate? = null
        
        for (periodDay in sortedPeriodDays) {
            if (lastDate == null || periodDay.date.toEpochDays() - lastDate.toEpochDays() == 1L) {
                currentPeriodLength++
            } else {
                if (currentPeriodLength > 0) {
                    periodLengths.add(currentPeriodLength)
                }
                currentPeriodLength = 1
            }
            lastDate = periodDay.date
        }
        if (currentPeriodLength > 0) {
            periodLengths.add(currentPeriodLength)
        }
        
        val averagePeriodLength = if (periodLengths.isNotEmpty()) {
            periodLengths.average()
        } else {
            userPrefs.averagePeriodLength.toDouble()
        }
        
        return CycleStatistics(
            averageCycleLength = averageCycleLength,
            averagePeriodLength = averagePeriodLength,
            totalCycles = cycleLengths.size,
            shortestCycle = cycleLengths.minOrNull() ?: userPrefs.averageCycleLength,
            longestCycle = cycleLengths.maxOrNull() ?: userPrefs.averageCycleLength,
            cycleLengths = cycleLengths
        )
    }
    
    private fun calculateSymptomTrends(
        symptoms: List<com.periodpal.app.data.entities.SymptomEntry>
    ): Map<SymptomType, SymptomTrend> {
        val trends = mutableMapOf<SymptomType, SymptomTrend>()
        
        val symptomsByType = symptoms.groupBy { it.symptomType }
        
        symptomsByType.forEach { (type, entries) ->
            val sortedEntries = entries.sortedBy { it.date }
            val values = sortedEntries.map { it.value.toDouble() }
            
            if (values.isNotEmpty()) {
                val average = values.average()
                val trend = if (values.size >= 2) {
                    val firstHalf = values.take(values.size / 2).average()
                    val secondHalf = values.drop(values.size / 2).average()
                    when {
                        secondHalf > firstHalf + 0.5 -> TrendDirection.INCREASING
                        secondHalf < firstHalf - 0.5 -> TrendDirection.DECREASING
                        else -> TrendDirection.STABLE
                    }
                } else {
                    TrendDirection.STABLE
                }
                
                trends[type] = SymptomTrend(
                    symptomType = type,
                    averageValue = average,
                    trendDirection = trend,
                    dataPoints = values,
                    frequency = entries.size
                )
            }
        }
        
        return trends
    }
    
    private suspend fun calculatePredictions(
        lastPeriodDay: com.periodpal.app.data.entities.CycleEntry?,
        userPrefs: com.periodpal.app.data.entities.UserPreferences
    ): PredictionData {
        return if (lastPeriodDay != null) {
            val nextPeriodDate = LocalDate.fromEpochDays(
                lastPeriodDay.date.toEpochDays() + userPrefs.averageCycleLength
            )
            val ovulationDate = LocalDate.fromEpochDays(
                lastPeriodDay.date.toEpochDays() + userPrefs.averageCycleLength - userPrefs.lutealPhaseLength
            )
            val fertileWindowStart = LocalDate.fromEpochDays(ovulationDate.toEpochDays() - 5)
            val fertileWindowEnd = LocalDate.fromEpochDays(ovulationDate.toEpochDays() + 1)
            
            PredictionData(
                nextPeriodDate = nextPeriodDate,
                ovulationDate = ovulationDate,
                fertileWindowStart = fertileWindowStart,
                fertileWindowEnd = fertileWindowEnd,
                daysUntilNextPeriod = (nextPeriodDate.toEpochDays() - today.toEpochDays()).toInt()
            )
        } else {
            PredictionData()
        }
    }
    
    private fun calculateCycleRegularity(
        periodDays: List<com.periodpal.app.data.entities.CycleEntry>
    ): CycleRegularity {
        // This is a simplified calculation - in a real app you'd want more sophisticated analysis
        val cycleLengths = mutableListOf<Int>()
        val sortedPeriodDays = periodDays.sortedBy { it.date }
        
        // Calculate cycle lengths (simplified)
        for (i in 1 until sortedPeriodDays.size) {
            val daysBetween = (sortedPeriodDays[i].date.toEpochDays() - 
                             sortedPeriodDays[i-1].date.toEpochDays()).toInt()
            if (daysBetween in 15..45) {
                cycleLengths.add(daysBetween)
            }
        }
        
        return if (cycleLengths.isEmpty()) {
            CycleRegularity.UNKNOWN
        } else {
            val variance = cycleLengths.map { (it - cycleLengths.average()).let { diff -> diff * diff } }.average()
            when {
                variance <= 4 -> CycleRegularity.VERY_REGULAR
                variance <= 9 -> CycleRegularity.REGULAR
                variance <= 16 -> CycleRegularity.SOMEWHAT_IRREGULAR
                else -> CycleRegularity.IRREGULAR
            }
        }
    }
    
    private fun calculateMostCommonSymptoms(
        symptoms: List<com.periodpal.app.data.entities.SymptomEntry>
    ): List<CommonSymptom> {
        return symptoms.groupBy { it.symptomType }
            .map { (type, entries) ->
                CommonSymptom(
                    symptomType = type,
                    frequency = entries.size,
                    averageIntensity = entries.map { it.value }.average()
                )
            }
            .sortedByDescending { it.frequency }
            .take(5)
    }
}

data class InsightsUiState(
    val isLoading: Boolean = true,
    val cycleStatistics: CycleStatistics = CycleStatistics(),
    val symptomTrends: Map<SymptomType, SymptomTrend> = emptyMap(),
    val predictions: PredictionData = PredictionData(),
    val cycleRegularity: CycleRegularity = CycleRegularity.UNKNOWN,
    val commonSymptoms: List<CommonSymptom> = emptyList(),
    val averageCycleLength: Double = 28.0,
    val averagePeriodLength: Double = 5.0,
    val error: String? = null
)

data class CycleStatistics(
    val averageCycleLength: Double = 28.0,
    val averagePeriodLength: Double = 5.0,
    val totalCycles: Int = 0,
    val shortestCycle: Int = 28,
    val longestCycle: Int = 28,
    val cycleLengths: List<Int> = emptyList()
)

data class SymptomTrend(
    val symptomType: SymptomType,
    val averageValue: Double,
    val trendDirection: TrendDirection,
    val dataPoints: List<Double>,
    val frequency: Int
)

data class PredictionData(
    val nextPeriodDate: LocalDate? = null,
    val ovulationDate: LocalDate? = null,
    val fertileWindowStart: LocalDate? = null,
    val fertileWindowEnd: LocalDate? = null,
    val daysUntilNextPeriod: Int = 0
)

data class CommonSymptom(
    val symptomType: SymptomType,
    val frequency: Int,
    val averageIntensity: Double
)

enum class TrendDirection {
    INCREASING,
    DECREASING,
    STABLE
}

enum class CycleRegularity {
    VERY_REGULAR,
    REGULAR,
    SOMEWHAT_IRREGULAR,
    IRREGULAR,
    UNKNOWN
}
