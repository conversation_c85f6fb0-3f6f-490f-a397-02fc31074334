package com.periodpal.app.ui.components

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.center
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.periodpal.app.ui.theme.FollicularGreen
import com.periodpal.app.ui.theme.LutealBlue
import com.periodpal.app.ui.theme.OvulationOrange
import com.periodpal.app.ui.theme.PeriodPalTheme
import com.periodpal.app.ui.theme.PeriodRed
import kotlin.math.cos
import kotlin.math.sin

@Composable
fun CyclePhaseIndicator(
    currentDay: Int,
    cycleLength: Int,
    periodLength: Int,
    currentPhase: CyclePhase,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(20.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(20.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // Circular progress indicator
            Box(
                contentAlignment = Alignment.Center,
                modifier = Modifier.size(200.dp)
            ) {
                Canvas(
                    modifier = Modifier.size(200.dp)
                ) {
                    drawCycleProgress(
                        currentDay = currentDay,
                        cycleLength = cycleLength,
                        periodLength = periodLength
                    )
                }
                
                // Center content
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = "Day $currentDay",
                        style = MaterialTheme.typography.headlineMedium,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.onSurface
                    )
                    Text(
                        text = "of $cycleLength",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = currentPhase.displayName,
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.SemiBold,
                        color = currentPhase.color,
                        textAlign = TextAlign.Center
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(20.dp))
            
            // Phase legend
            Row(
                horizontalArrangement = Arrangement.SpaceEvenly,
                modifier = Modifier.fillMaxWidth()
            ) {
                CyclePhase.values().forEach { phase ->
                    PhaseLegendItem(
                        phase = phase,
                        isActive = phase == currentPhase
                    )
                }
            }
        }
    }
}

@Composable
fun PhaseLegendItem(
    phase: CyclePhase,
    isActive: Boolean,
    modifier: Modifier = Modifier
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = modifier
    ) {
        Box(
            modifier = Modifier
                .size(12.dp)
                .clip(CircleShape)
                .background(
                    if (isActive) phase.color else phase.color.copy(alpha = 0.3f)
                )
        )
        Spacer(modifier = Modifier.height(4.dp))
        Text(
            text = phase.shortName,
            style = MaterialTheme.typography.bodySmall,
            color = if (isActive) phase.color else MaterialTheme.colorScheme.onSurfaceVariant,
            fontWeight = if (isActive) FontWeight.SemiBold else FontWeight.Normal
        )
    }
}

private fun DrawScope.drawCycleProgress(
    currentDay: Int,
    cycleLength: Int,
    periodLength: Int
) {
    val strokeWidth = 16.dp.toPx()
    val radius = (size.minDimension - strokeWidth) / 2
    val center = size.center
    
    // Background circle
    drawCircle(
        color = Color.Gray.copy(alpha = 0.1f),
        radius = radius,
        center = center,
        style = Stroke(width = strokeWidth)
    )
    
    // Calculate phase segments
    val periodAngle = (periodLength.toFloat() / cycleLength) * 360f
    val follicularAngle = ((14 - periodLength).toFloat() / cycleLength) * 360f
    val ovulationAngle = (3.toFloat() / cycleLength) * 360f
    val lutealAngle = 360f - periodAngle - follicularAngle - ovulationAngle
    
    var startAngle = -90f // Start from top
    
    // Draw period phase
    drawArc(
        color = PeriodRed,
        startAngle = startAngle,
        sweepAngle = periodAngle,
        useCenter = false,
        style = Stroke(width = strokeWidth),
        topLeft = Offset(center.x - radius, center.y - radius),
        size = androidx.compose.ui.geometry.Size(radius * 2, radius * 2)
    )
    startAngle += periodAngle
    
    // Draw follicular phase
    drawArc(
        color = FollicularGreen,
        startAngle = startAngle,
        sweepAngle = follicularAngle,
        useCenter = false,
        style = Stroke(width = strokeWidth),
        topLeft = Offset(center.x - radius, center.y - radius),
        size = androidx.compose.ui.geometry.Size(radius * 2, radius * 2)
    )
    startAngle += follicularAngle
    
    // Draw ovulation phase
    drawArc(
        color = OvulationOrange,
        startAngle = startAngle,
        sweepAngle = ovulationAngle,
        useCenter = false,
        style = Stroke(width = strokeWidth),
        topLeft = Offset(center.x - radius, center.y - radius),
        size = androidx.compose.ui.geometry.Size(radius * 2, radius * 2)
    )
    startAngle += ovulationAngle
    
    // Draw luteal phase
    drawArc(
        color = LutealBlue,
        startAngle = startAngle,
        sweepAngle = lutealAngle,
        useCenter = false,
        style = Stroke(width = strokeWidth),
        topLeft = Offset(center.x - radius, center.y - radius),
        size = androidx.compose.ui.geometry.Size(radius * 2, radius * 2)
    )
    
    // Draw current day indicator
    val currentAngle = ((currentDay.toFloat() / cycleLength) * 360f - 90f) * (Math.PI / 180f)
    val indicatorRadius = radius + strokeWidth / 2
    val indicatorX = center.x + cos(currentAngle).toFloat() * indicatorRadius
    val indicatorY = center.y + sin(currentAngle).toFloat() * indicatorRadius
    
    drawCircle(
        color = Color.White,
        radius = 8.dp.toPx(),
        center = Offset(indicatorX, indicatorY)
    )
    drawCircle(
        color = MaterialTheme.colorScheme.primary,
        radius = 6.dp.toPx(),
        center = Offset(indicatorX, indicatorY)
    )
}

enum class CyclePhase(
    val displayName: String,
    val shortName: String,
    val color: Color
) {
    PERIOD("Menstrual", "Period", PeriodRed),
    FOLLICULAR("Follicular", "Follicular", FollicularGreen),
    OVULATION("Ovulation", "Ovulation", OvulationOrange),
    LUTEAL("Luteal", "Luteal", LutealBlue)
}

@Preview(showBackground = true)
@Composable
fun CyclePhaseIndicatorPreview() {
    PeriodPalTheme {
        CyclePhaseIndicator(
            currentDay = 8,
            cycleLength = 28,
            periodLength = 5,
            currentPhase = CyclePhase.FOLLICULAR,
            modifier = Modifier.padding(16.dp)
        )
    }
}
