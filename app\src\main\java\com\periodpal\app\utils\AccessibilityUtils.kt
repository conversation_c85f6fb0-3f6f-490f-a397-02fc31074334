package com.periodpal.app.utils

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.luminance
import kotlin.math.max
import kotlin.math.min

/**
 * Utility functions for accessibility compliance
 */
object AccessibilityUtils {
    
    /**
     * Calculate contrast ratio between two colors
     * WCAG AA requires a contrast ratio of at least 4.5:1 for normal text
     * WCAG AAA requires a contrast ratio of at least 7:1 for normal text
     */
    fun calculateContrastRatio(color1: Color, color2: Color): Double {
        val luminance1 = color1.luminance()
        val luminance2 = color2.luminance()
        
        val lighter = max(luminance1, luminance2)
        val darker = min(luminance1, luminance2)
        
        return (lighter + 0.05) / (darker + 0.05)
    }
    
    /**
     * Check if color combination meets WCAG AA standards
     */
    fun meetsWCAGAA(foreground: Color, background: Color): Boolean {
        return calculateContrastRatio(foreground, background) >= 4.5
    }
    
    /**
     * Check if color combination meets WCAG AAA standards
     */
    fun meetsWCAGAAA(foreground: Color, background: Color): Boolean {
        return calculateContrastRatio(foreground, background) >= 7.0
    }
    
    /**
     * Get accessible text color for a given background
     */
    fun getAccessibleTextColor(backgroundColor: Color): Color {
        val whiteContrast = calculateContrastRatio(Color.White, backgroundColor)
        val blackContrast = calculateContrastRatio(Color.Black, backgroundColor)
        
        return if (whiteContrast > blackContrast) Color.White else Color.Black
    }
    
    /**
     * Ensure minimum touch target size (44dp x 44dp as per Material Design)
     */
    const val MIN_TOUCH_TARGET_SIZE_DP = 44
    
    /**
     * Content descriptions for common UI elements
     */
    object ContentDescriptions {
        const val PERIOD_START_BUTTON = "Mark period start"
        const val PERIOD_END_BUTTON = "Mark period end"
        const val SYMPTOM_CARD = "Symptom tracking card"
        const val CALENDAR_DAY_PERIOD = "Period day"
        const val CALENDAR_DAY_OVULATION = "Ovulation day"
        const val CALENDAR_DAY_FERTILE = "Fertile window day"
        const val CALENDAR_DAY_PMS = "PMS day"
        const val CYCLE_PHASE_INDICATOR = "Current cycle phase: %s, day %d of %d"
        const val NAVIGATION_TAB = "Navigate to %s"
        const val EMERGENCY_CONTACT_CALL = "Call %s"
        const val SETTINGS_TOGGLE = "Toggle %s setting"
        const val SLIDER_VALUE = "%s: %s"
    }
    
    /**
     * Semantic labels for screen readers
     */
    object SemanticLabels {
        const val MAIN_CONTENT = "Main content"
        const val NAVIGATION = "Bottom navigation"
        const val DASHBOARD = "Dashboard screen"
        const val CALENDAR = "Calendar screen"
        const val HEALTH_LOGS = "Health logs screen"
        const val INSIGHTS = "Insights screen"
        const val SETTINGS = "Settings screen"
        const val SYMPTOM_LOGGING = "Symptom logging section"
        const val CYCLE_TRACKING = "Cycle tracking section"
        const val PREDICTIONS = "Predictions section"
        const val EMERGENCY_CONTACTS = "Emergency contacts section"
    }
    
    /**
     * Announcements for screen readers
     */
    object Announcements {
        const val PERIOD_LOGGED = "Period day logged"
        const val PERIOD_REMOVED = "Period day removed"
        const val SYMPTOM_LOGGED = "Symptom logged successfully"
        const val SYMPTOM_UPDATED = "Symptom updated"
        const val CONTACT_ADDED = "Emergency contact added"
        const val CONTACT_UPDATED = "Emergency contact updated"
        const val CONTACT_DELETED = "Emergency contact deleted"
        const val SETTINGS_SAVED = "Settings saved"
        const val DATA_LOADING = "Loading data"
        const val DATA_LOADED = "Data loaded successfully"
        const val ERROR_OCCURRED = "An error occurred"
    }
}
