package com.periodpal.app.ui.dashboard

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.periodpal.app.data.entities.SymptomType
import com.periodpal.app.data.repository.CycleRepository
import com.periodpal.app.data.repository.UserPreferencesRepository
import com.periodpal.app.ui.components.CyclePhase
import com.periodpal.app.ui.components.SymptomOption
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.launch
import kotlinx.datetime.Clock
import kotlinx.datetime.LocalDate
import kotlinx.datetime.TimeZone
import kotlinx.datetime.todayIn
import javax.inject.Inject

@HiltViewModel
class DashboardViewModel @Inject constructor(
    private val cycleRepository: CycleRepository,
    private val userPreferencesRepository: UserPreferencesRepository
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(DashboardUiState())
    val uiState: StateFlow<DashboardUiState> = _uiState.asStateFlow()
    
    private val today = Clock.System.todayIn(TimeZone.currentSystemDefault())
    
    init {
        loadDashboardData()
    }
    
    private fun loadDashboardData() {
        viewModelScope.launch {
            combine(
                cycleRepository.getAllCycleEntries(),
                cycleRepository.getAllSymptomEntries(),
                userPreferencesRepository.getUserPreferences()
            ) { cycleEntries, symptomEntries, preferences ->
                val userPrefs = preferences ?: return@combine
                
                // Calculate current cycle information
                val lastPeriodDay = cycleRepository.getLastPeriodDay()
                val currentCycleDay = lastPeriodDay?.let { lastPeriod ->
                    val daysSinceLastPeriod = today.toEpochDays() - lastPeriod.date.toEpochDays()
                    (daysSinceLastPeriod + 1).toInt()
                } ?: 1
                
                val currentPhase = calculateCurrentPhase(
                    currentCycleDay,
                    userPrefs.averageCycleLength,
                    userPrefs.averagePeriodLength,
                    userPrefs.lutealPhaseLength
                )
                
                // Get today's symptoms
                val todaysSymptoms = cycleRepository.getSymptomEntriesByDate(today)
                val flowSymptom = todaysSymptoms.find { it.symptomType == SymptomType.FLOW_INTENSITY }
                val moodSymptom = todaysSymptoms.find { it.symptomType == SymptomType.MOOD }
                val painSymptom = todaysSymptoms.find { it.symptomType == SymptomType.PAIN_LEVEL }
                
                // Calculate predictions
                val nextPeriodDate = lastPeriodDay?.let { lastPeriod ->
                    LocalDate.fromEpochDays(
                        lastPeriod.date.toEpochDays() + userPrefs.averageCycleLength
                    )
                }
                
                val fertileWindowStart = lastPeriodDay?.let { lastPeriod ->
                    LocalDate.fromEpochDays(
                        lastPeriod.date.toEpochDays() + userPrefs.averageCycleLength - userPrefs.lutealPhaseLength - 5
                    )
                }
                
                val fertileWindowEnd = lastPeriodDay?.let { lastPeriod ->
                    LocalDate.fromEpochDays(
                        lastPeriod.date.toEpochDays() + userPrefs.averageCycleLength - userPrefs.lutealPhaseLength + 1
                    )
                }
                
                DashboardUiState(
                    isLoading = false,
                    currentDay = currentCycleDay,
                    cycleLength = userPrefs.averageCycleLength,
                    periodLength = userPrefs.averagePeriodLength,
                    currentPhase = currentPhase,
                    isPeriodDay = cycleRepository.getCycleEntryByDate(today)?.isPeriodDay == true,
                    selectedFlowOption = flowSymptom?.let { symptom ->
                        com.periodpal.app.ui.components.SymptomOptions.flowOptions.find { it.value == symptom.value }
                    },
                    selectedMoodOption = moodSymptom?.let { symptom ->
                        com.periodpal.app.ui.components.SymptomOptions.moodOptions.find { it.value == symptom.value }
                    },
                    selectedPainOption = painSymptom?.let { symptom ->
                        com.periodpal.app.ui.components.SymptomOptions.painOptions.find { it.value == symptom.value }
                    },
                    nextPeriodDate = nextPeriodDate,
                    fertileWindowStart = fertileWindowStart,
                    fertileWindowEnd = fertileWindowEnd,
                    lastPeriodDate = lastPeriodDay?.date
                )
            }.collect { newState ->
                _uiState.value = newState
            }
        }
    }
    
    private fun calculateCurrentPhase(
        currentDay: Int,
        cycleLength: Int,
        periodLength: Int,
        lutealPhaseLength: Int
    ): CyclePhase {
        return when {
            currentDay <= periodLength -> CyclePhase.PERIOD
            currentDay <= cycleLength - lutealPhaseLength - 3 -> CyclePhase.FOLLICULAR
            currentDay <= cycleLength - lutealPhaseLength + 1 -> CyclePhase.OVULATION
            else -> CyclePhase.LUTEAL
        }
    }
    
    fun togglePeriodDay() {
        viewModelScope.launch {
            val currentState = _uiState.value
            if (currentState.isPeriodDay) {
                cycleRepository.removePeriodDay(today)
            } else {
                cycleRepository.logPeriodDay(today)
            }
        }
    }
    
    fun logFlowIntensity(option: SymptomOption) {
        viewModelScope.launch {
            cycleRepository.logSymptom(
                date = today,
                symptomType = SymptomType.FLOW_INTENSITY,
                value = option.value
            )
        }
    }
    
    fun logMood(option: SymptomOption) {
        viewModelScope.launch {
            cycleRepository.logSymptom(
                date = today,
                symptomType = SymptomType.MOOD,
                value = option.value
            )
        }
    }
    
    fun logPainLevel(option: SymptomOption) {
        viewModelScope.launch {
            cycleRepository.logSymptom(
                date = today,
                symptomType = SymptomType.PAIN_LEVEL,
                value = option.value
            )
        }
    }
}

data class DashboardUiState(
    val isLoading: Boolean = true,
    val currentDay: Int = 1,
    val cycleLength: Int = 28,
    val periodLength: Int = 5,
    val currentPhase: CyclePhase = CyclePhase.PERIOD,
    val isPeriodDay: Boolean = false,
    val selectedFlowOption: SymptomOption? = null,
    val selectedMoodOption: SymptomOption? = null,
    val selectedPainOption: SymptomOption? = null,
    val nextPeriodDate: LocalDate? = null,
    val fertileWindowStart: LocalDate? = null,
    val fertileWindowEnd: LocalDate? = null,
    val lastPeriodDate: LocalDate? = null,
    val error: String? = null
)
