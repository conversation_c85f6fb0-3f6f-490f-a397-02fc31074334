package com.periodpal.app.data.entities

import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "emergency_contacts")
data class EmergencyContact(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val name: String,
    val phoneNumber: String,
    val relationship: String, // Doctor, Partner, Family, Friend, etc.
    val isPrimary: Boolean = false,
    val notes: String? = null,
    val createdAt: kotlinx.datetime.Instant = kotlinx.datetime.Clock.System.now(),
    val updatedAt: kotlinx.datetime.Instant = kotlinx.datetime.Clock.System.now()
)
