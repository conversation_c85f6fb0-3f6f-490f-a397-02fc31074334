package com.periodpal.app.data.entities

import androidx.room.Entity
import androidx.room.ForeignKey
import androidx.room.Index
import androidx.room.PrimaryKey
import kotlinx.datetime.LocalDate

@Entity(
    tableName = "symptom_entries",
    foreignKeys = [
        ForeignKey(
            entity = CycleEntry::class,
            parentColumns = ["id"],
            childColumns = ["cycleEntryId"],
            onDelete = ForeignKey.CASCADE
        )
    ],
    indices = [Index(value = ["cycleEntryId"]), Index(value = ["date"])]
)
data class SymptomEntry(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val cycleEntryId: Long,
    val date: LocalDate,
    val symptomType: SymptomType,
    val value: Int, // Intensity/severity level
    val notes: String? = null,
    val createdAt: kotlinx.datetime.Instant = kotlinx.datetime.Clock.System.now()
)

enum class SymptomType {
    FLOW_INTENSITY,
    MOOD,
    PAIN_LEVEL,
    CRAMPS,
    HEADACHE,
    BLOATING,
    BREAST_TENDERNESS,
    ACNE,
    ENERGY_LEVEL,
    SLEEP_QUALITY,
    APPETITE,
    NAUSEA,
    BACK_PAIN,
    TEMPERATURE,
    CERVICAL_MUCUS,
    SPOTTING,
    CUSTOM
}
