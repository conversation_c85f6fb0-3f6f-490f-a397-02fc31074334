package com.periodpal.app.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Favorite
import androidx.compose.material.icons.filled.Face
import androidx.compose.material.icons.filled.LocalHospital
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.periodpal.app.ui.theme.FlowHeavy
import com.periodpal.app.ui.theme.FlowLight
import com.periodpal.app.ui.theme.FlowMedium
import com.periodpal.app.ui.theme.MoodHappy
import com.periodpal.app.ui.theme.MoodIrritable
import com.periodpal.app.ui.theme.MoodSad
import com.periodpal.app.ui.theme.PainMild
import com.periodpal.app.ui.theme.PainModerate
import com.periodpal.app.ui.theme.PainNone
import com.periodpal.app.ui.theme.PainSevere
import com.periodpal.app.ui.theme.PeriodPalTheme

@Composable
fun SymptomCard(
    title: String,
    icon: ImageVector,
    options: List<SymptomOption>,
    selectedOption: SymptomOption?,
    onOptionSelected: (SymptomOption) -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.fillMaxWidth()
            ) {
                Icon(
                    imageVector = icon,
                    contentDescription = title,
                    tint = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.size(24.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = title,
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.SemiBold,
                    color = MaterialTheme.colorScheme.onSurface
                )
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                modifier = Modifier.fillMaxWidth()
            ) {
                options.forEach { option ->
                    SymptomOptionButton(
                        option = option,
                        isSelected = selectedOption == option,
                        onClick = { onOptionSelected(option) },
                        modifier = Modifier.weight(1f)
                    )
                }
            }
        }
    }
}

@Composable
fun SymptomOptionButton(
    option: SymptomOption,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = modifier
            .clickable { onClick() }
            .padding(4.dp)
    ) {
        Box(
            modifier = Modifier
                .size(44.dp)
                .clip(CircleShape)
                .background(
                    if (isSelected) option.color else option.color.copy(alpha = 0.3f)
                ),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = option.emoji,
                style = MaterialTheme.typography.titleMedium
            )
        }
        
        Spacer(modifier = Modifier.height(4.dp))
        
        Text(
            text = option.label,
            style = MaterialTheme.typography.bodySmall,
            color = if (isSelected) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.onSurfaceVariant,
            fontWeight = if (isSelected) FontWeight.SemiBold else FontWeight.Normal
        )
    }
}

data class SymptomOption(
    val id: String,
    val label: String,
    val emoji: String,
    val color: Color,
    val value: Int
)

// Predefined symptom options
object SymptomOptions {
    val flowOptions = listOf(
        SymptomOption("flow_light", "Light", "💧", FlowLight, 1),
        SymptomOption("flow_medium", "Medium", "💧💧", FlowMedium, 2),
        SymptomOption("flow_heavy", "Heavy", "💧💧💧", FlowHeavy, 3)
    )

    val moodOptions = listOf(
        SymptomOption("mood_happy", "Happy", "😊", MoodHappy, 1),
        SymptomOption("mood_sad", "Sad", "😢", MoodSad, 2),
        SymptomOption("mood_irritable", "Irritable", "😤", MoodIrritable, 3),
        SymptomOption("mood_anxious", "Anxious", "😰", MoodAnxious, 4)
    )

    val painOptions = listOf(
        SymptomOption("pain_none", "None", "😌", PainNone, 0),
        SymptomOption("pain_mild", "Mild", "😐", PainMild, 1),
        SymptomOption("pain_moderate", "Moderate", "😣", PainModerate, 2),
        SymptomOption("pain_severe", "Severe", "😫", PainSevere, 3)
    )

    val energyOptions = listOf(
        SymptomOption("energy_very_low", "Very Low", "😴", PainSevere, 0),
        SymptomOption("energy_low", "Low", "😑", PainModerate, 1),
        SymptomOption("energy_normal", "Normal", "😊", MoodHappy, 2),
        SymptomOption("energy_high", "High", "😄", MoodHappy, 3),
        SymptomOption("energy_very_high", "Very High", "⚡", MoodHappy, 4)
    )

    val sleepOptions = listOf(
        SymptomOption("sleep_very_poor", "Very Poor", "😵", PainSevere, 0),
        SymptomOption("sleep_poor", "Poor", "😴", PainModerate, 1),
        SymptomOption("sleep_fair", "Fair", "😐", PainMild, 2),
        SymptomOption("sleep_good", "Good", "😊", MoodHappy, 3),
        SymptomOption("sleep_excellent", "Excellent", "😇", MoodHappy, 4)
    )

    val appetiteOptions = listOf(
        SymptomOption("appetite_very_low", "Very Low", "🚫", PainSevere, 0),
        SymptomOption("appetite_low", "Low", "😐", PainModerate, 1),
        SymptomOption("appetite_normal", "Normal", "😊", MoodHappy, 2),
        SymptomOption("appetite_high", "High", "😋", MoodHappy, 3),
        SymptomOption("appetite_very_high", "Very High", "🤤", MoodHappy, 4)
    )
}

@Preview(showBackground = true)
@Composable
fun SymptomCardPreview() {
    PeriodPalTheme {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            SymptomCard(
                title = "Flow Intensity",
                icon = Icons.Default.Favorite,
                options = SymptomOptions.flowOptions,
                selectedOption = SymptomOptions.flowOptions[1],
                onOptionSelected = { }
            )
            
            SymptomCard(
                title = "Mood",
                icon = Icons.Default.Face,
                options = SymptomOptions.moodOptions,
                selectedOption = null,
                onOptionSelected = { }
            )
            
            SymptomCard(
                title = "Pain Level",
                icon = Icons.Default.LocalHospital,
                options = SymptomOptions.painOptions,
                selectedOption = SymptomOptions.painOptions[0],
                onOptionSelected = { }
            )
        }
    }
}
