<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Base.Theme.PeriodPal" parent="Theme.Material3.DayNight.NoActionBar">
        <!-- Customize your light theme here. -->
        <item name="colorPrimary">@color/rose_pink</item>
        <item name="colorPrimaryVariant">@color/rose_pink_dark</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <item name="colorSecondary">@color/soft_purple</item>
        <item name="colorOnSecondary">@color/white</item>
        
        <item name="android:colorBackground">@color/cream_white</item>
        <item name="colorOnBackground">@color/gray_dark</item>
        
        <item name="colorSurface">@color/white</item>
        <item name="colorOnSurface">@color/gray_dark</item>
        
        <!-- Status bar color. -->
        <item name="android:statusBarColor">@color/rose_pink_light</item>
        <item name="android:windowLightStatusBar">true</item>
    </style>

    <style name="Theme.PeriodPal" parent="Base.Theme.PeriodPal" />
</resources>
