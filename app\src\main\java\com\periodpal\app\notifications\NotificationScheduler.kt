package com.periodpal.app.notifications

import android.content.Context
import androidx.work.CoroutineWorker
import androidx.work.ExistingPeriodicWorkPolicy
import androidx.work.PeriodicWorkRequestBuilder
import androidx.work.WorkManager
import androidx.work.WorkerParameters
import com.periodpal.app.data.repository.CycleRepository
import com.periodpal.app.data.repository.UserPreferencesRepository
import dagger.assisted.Assisted
import dagger.assisted.AssistedInject
import kotlinx.coroutines.flow.first
import kotlinx.datetime.Clock
import kotlinx.datetime.DateTimeUnit
import kotlinx.datetime.LocalDate
import kotlinx.datetime.TimeZone
import kotlinx.datetime.plus
import kotlinx.datetime.todayIn
import java.util.concurrent.TimeUnit

class NotificationScheduler @AssistedInject constructor(
    @Assisted context: Context,
    @Assisted workerParams: WorkerParameters,
    private val cycleRepository: CycleRepository,
    private val userPreferencesRepository: UserPreferencesRepository,
    private val notificationHelper: NotificationHelper
) : CoroutineWorker(context, workerParams) {
    
    companion object {
        const val WORK_NAME = "period_pal_notifications"
        
        fun scheduleNotifications(context: Context) {
            val workRequest = PeriodicWorkRequestBuilder<NotificationScheduler>(
                repeatInterval = 1,
                repeatIntervalTimeUnit = TimeUnit.DAYS
            ).build()
            
            WorkManager.getInstance(context).enqueueUniquePeriodicWork(
                WORK_NAME,
                ExistingPeriodicWorkPolicy.KEEP,
                workRequest
            )
        }
        
        fun cancelNotifications(context: Context) {
            WorkManager.getInstance(context).cancelUniqueWork(WORK_NAME)
        }
    }
    
    override suspend fun doWork(): Result {
        return try {
            val userPrefs = userPreferencesRepository.getUserPreferences().first()
            
            if (userPrefs?.notificationsEnabled != true) {
                return Result.success()
            }
            
            val today = Clock.System.todayIn(TimeZone.currentSystemDefault())
            checkAndSendNotifications(today, userPrefs)
            
            Result.success()
        } catch (exception: Exception) {
            Result.failure()
        }
    }
    
    private suspend fun checkAndSendNotifications(
        today: LocalDate,
        userPrefs: com.periodpal.app.data.entities.UserPreferences
    ) {
        val lastPeriodDay = cycleRepository.getLastPeriodDay()
        
        if (lastPeriodDay != null) {
            // Calculate next period date
            val nextPeriodDate = lastPeriodDay.date.plus(
                userPrefs.averageCycleLength,
                DateTimeUnit.DAY
            )
            
            // Check for period reminder
            val daysUntilPeriod = nextPeriodDate.toEpochDays() - today.toEpochDays()
            if (daysUntilPeriod.toInt() == userPrefs.periodReminderDays) {
                notificationHelper.showPeriodReminderNotification(userPrefs.periodReminderDays)
            }
            
            // Check for fertile window reminder
            if (userPrefs.fertileWindowReminder) {
                val ovulationDate = lastPeriodDay.date.plus(
                    userPrefs.averageCycleLength - userPrefs.lutealPhaseLength,
                    DateTimeUnit.DAY
                )
                
                val fertileWindowStart = ovulationDate.plus(-5, DateTimeUnit.DAY)
                val fertileWindowEnd = ovulationDate.plus(1, DateTimeUnit.DAY)
                
                when {
                    today == fertileWindowStart -> {
                        notificationHelper.showFertileWindowNotification()
                    }
                    today == ovulationDate -> {
                        notificationHelper.showOvulationReminderNotification()
                    }
                }
            }
        }
        
        // Check for symptom logging reminder (every 3 days if no symptoms logged recently)
        val recentSymptoms = cycleRepository.getSymptomEntriesByDate(today)
        val yesterdaySymptoms = cycleRepository.getSymptomEntriesByDate(
            today.plus(-1, DateTimeUnit.DAY)
        )
        
        if (recentSymptoms.isEmpty() && yesterdaySymptoms.isEmpty()) {
            // Check if it's been 3 days since last symptom log
            val threeDaysAgo = today.plus(-3, DateTimeUnit.DAY)
            val symptomsSince = cycleRepository.getSymptomEntriesInRange(threeDaysAgo, today)
            
            if (symptomsSince.isEmpty()) {
                notificationHelper.showSymptomReminderNotification()
            }
        }
    }
}
