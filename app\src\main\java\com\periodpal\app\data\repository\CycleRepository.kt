package com.periodpal.app.data.repository

import com.periodpal.app.data.dao.CycleEntryDao
import com.periodpal.app.data.dao.SymptomEntryDao
import com.periodpal.app.data.entities.CycleEntry
import com.periodpal.app.data.entities.SymptomEntry
import com.periodpal.app.data.entities.SymptomType
import kotlinx.coroutines.flow.Flow
import kotlinx.datetime.LocalDate
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class CycleRepository @Inject constructor(
    private val cycleEntryDao: CycleEntryDao,
    private val symptomEntryDao: SymptomEntryDao
) {
    
    // Cycle Entry operations
    fun getAllCycleEntries(): Flow<List<CycleEntry>> = cycleEntryDao.getAllCycleEntries()
    
    suspend fun getCycleEntryByDate(date: LocalDate): CycleEntry? = 
        cycleEntryDao.getCycleEntryByDate(date)
    
    suspend fun getCycleEntriesInRange(startDate: LocalDate, endDate: LocalDate): List<CycleEntry> =
        cycleEntryDao.getCycleEntriesInRange(startDate, endDate)
    
    fun getPeriodDays(): Flow<List<CycleEntry>> = cycleEntryDao.getPeriodDays()
    
    suspend fun getLastPeriodDay(): CycleEntry? = cycleEntryDao.getLastPeriodDay()
    
    suspend fun getAverageCycleLength(): Double? = cycleEntryDao.getAverageCycleLength()
    
    suspend fun insertCycleEntry(cycleEntry: CycleEntry): Long = 
        cycleEntryDao.insertCycleEntry(cycleEntry)
    
    suspend fun updateCycleEntry(cycleEntry: CycleEntry) = 
        cycleEntryDao.updateCycleEntry(cycleEntry)
    
    suspend fun deleteCycleEntry(cycleEntry: CycleEntry) = 
        cycleEntryDao.deleteCycleEntry(cycleEntry)
    
    // Symptom Entry operations
    fun getAllSymptomEntries(): Flow<List<SymptomEntry>> = symptomEntryDao.getAllSymptomEntries()
    
    suspend fun getSymptomEntriesByDate(date: LocalDate): List<SymptomEntry> =
        symptomEntryDao.getSymptomEntriesByDate(date)
    
    suspend fun getSymptomEntryByDateAndType(date: LocalDate, symptomType: SymptomType): SymptomEntry? =
        symptomEntryDao.getSymptomEntryByDateAndType(date, symptomType)
    
    fun getSymptomEntriesByType(symptomType: SymptomType): Flow<List<SymptomEntry>> =
        symptomEntryDao.getSymptomEntriesByType(symptomType)
    
    suspend fun insertSymptomEntry(symptomEntry: SymptomEntry): Long =
        symptomEntryDao.insertSymptomEntry(symptomEntry)
    
    suspend fun updateSymptomEntry(symptomEntry: SymptomEntry) =
        symptomEntryDao.updateSymptomEntry(symptomEntry)
    
    suspend fun deleteSymptomEntry(symptomEntry: SymptomEntry) =
        symptomEntryDao.deleteSymptomEntry(symptomEntry)
    
    // Combined operations
    suspend fun logPeriodDay(date: LocalDate, notes: String? = null): Long {
        val existingEntry = getCycleEntryByDate(date)
        return if (existingEntry != null) {
            val updatedEntry = existingEntry.copy(
                isPeriodDay = true,
                notes = notes,
                updatedAt = kotlinx.datetime.Clock.System.now()
            )
            updateCycleEntry(updatedEntry)
            existingEntry.id
        } else {
            val newEntry = CycleEntry(
                date = date,
                isPeriodDay = true,
                notes = notes
            )
            insertCycleEntry(newEntry)
        }
    }
    
    suspend fun logSymptom(
        date: LocalDate,
        symptomType: SymptomType,
        value: Int,
        notes: String? = null
    ) {
        // Ensure cycle entry exists
        val cycleEntryId = getCycleEntryByDate(date)?.id ?: run {
            val newCycleEntry = CycleEntry(date = date)
            insertCycleEntry(newCycleEntry)
        }
        
        // Insert or update symptom entry
        val existingSymptom = getSymptomEntryByDateAndType(date, symptomType)
        if (existingSymptom != null) {
            val updatedSymptom = existingSymptom.copy(
                value = value,
                notes = notes,
                createdAt = kotlinx.datetime.Clock.System.now()
            )
            updateSymptomEntry(updatedSymptom)
        } else {
            val newSymptom = SymptomEntry(
                cycleEntryId = cycleEntryId,
                date = date,
                symptomType = symptomType,
                value = value,
                notes = notes
            )
            insertSymptomEntry(newSymptom)
        }
    }
    
    suspend fun removePeriodDay(date: LocalDate) {
        val existingEntry = getCycleEntryByDate(date)
        if (existingEntry != null) {
            val updatedEntry = existingEntry.copy(
                isPeriodDay = false,
                updatedAt = kotlinx.datetime.Clock.System.now()
            )
            updateCycleEntry(updatedEntry)
        }
    }
}
