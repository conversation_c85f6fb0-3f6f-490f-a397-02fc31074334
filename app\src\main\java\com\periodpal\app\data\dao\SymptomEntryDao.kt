package com.periodpal.app.data.dao

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update
import com.periodpal.app.data.entities.SymptomEntry
import com.periodpal.app.data.entities.SymptomType
import kotlinx.coroutines.flow.Flow
import kotlinx.datetime.LocalDate

@Dao
interface SymptomEntryDao {
    
    @Query("SELECT * FROM symptom_entries ORDER BY date DESC, createdAt DESC")
    fun getAllSymptomEntries(): Flow<List<SymptomEntry>>
    
    @Query("SELECT * FROM symptom_entries WHERE date = :date")
    suspend fun getSymptomEntriesByDate(date: LocalDate): List<SymptomEntry>
    
    @Query("SELECT * FROM symptom_entries WHERE date = :date AND symptomType = :symptomType LIMIT 1")
    suspend fun getSymptomEntryByDateAndType(date: LocalDate, symptomType: SymptomType): SymptomEntry?
    
    @Query("SELECT * FROM symptom_entries WHERE cycleEntryId = :cycleEntryId")
    suspend fun getSymptomEntriesByCycleEntry(cycleEntryId: Long): List<SymptomEntry>
    
    @Query("SELECT * FROM symptom_entries WHERE symptomType = :symptomType ORDER BY date DESC")
    fun getSymptomEntriesByType(symptomType: SymptomType): Flow<List<SymptomEntry>>
    
    @Query("SELECT * FROM symptom_entries WHERE date BETWEEN :startDate AND :endDate ORDER BY date ASC")
    suspend fun getSymptomEntriesInRange(startDate: LocalDate, endDate: LocalDate): List<SymptomEntry>
    
    @Query("""
        SELECT * FROM symptom_entries 
        WHERE symptomType = :symptomType 
        AND date BETWEEN :startDate AND :endDate 
        ORDER BY date ASC
    """)
    suspend fun getSymptomEntriesByTypeInRange(
        symptomType: SymptomType,
        startDate: LocalDate,
        endDate: LocalDate
    ): List<SymptomEntry>
    
    @Query("SELECT AVG(value) FROM symptom_entries WHERE symptomType = :symptomType")
    suspend fun getAverageSymptomValue(symptomType: SymptomType): Double?
    
    @Query("""
        SELECT symptomType, AVG(value) as avgValue 
        FROM symptom_entries 
        WHERE date BETWEEN :startDate AND :endDate 
        GROUP BY symptomType
    """)
    suspend fun getAverageSymptomValuesInRange(startDate: LocalDate, endDate: LocalDate): Map<SymptomType, Double>
    
    @Query("SELECT DISTINCT symptomType FROM symptom_entries ORDER BY symptomType")
    suspend fun getAllTrackedSymptomTypes(): List<SymptomType>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertSymptomEntry(symptomEntry: SymptomEntry): Long
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertSymptomEntries(symptomEntries: List<SymptomEntry>)
    
    @Update
    suspend fun updateSymptomEntry(symptomEntry: SymptomEntry)
    
    @Delete
    suspend fun deleteSymptomEntry(symptomEntry: SymptomEntry)
    
    @Query("DELETE FROM symptom_entries WHERE date = :date")
    suspend fun deleteSymptomEntriesByDate(date: LocalDate)
    
    @Query("DELETE FROM symptom_entries WHERE date = :date AND symptomType = :symptomType")
    suspend fun deleteSymptomEntryByDateAndType(date: LocalDate, symptomType: SymptomType)
    
    @Query("DELETE FROM symptom_entries")
    suspend fun deleteAllSymptomEntries()
}
