package com.periodpal.app.ui.settings

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.periodpal.app.data.entities.EmergencyContact
import com.periodpal.app.data.entities.UserPreferences
import com.periodpal.app.data.repository.UserPreferencesRepository
import com.periodpal.app.data.dao.EmergencyContactDao
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class SettingsViewModel @Inject constructor(
    private val userPreferencesRepository: UserPreferencesRepository,
    private val emergencyContactDao: EmergencyContactDao
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(SettingsUiState())
    val uiState: StateFlow<SettingsUiState> = _uiState.asStateFlow()
    
    init {
        loadSettings()
    }
    
    private fun loadSettings() {
        viewModelScope.launch {
            combine(
                userPreferencesRepository.getUserPreferences(),
                emergencyContactDao.getAllEmergencyContacts()
            ) { preferences, emergencyContacts ->
                SettingsUiState(
                    isLoading = false,
                    userPreferences = preferences,
                    emergencyContacts = emergencyContacts
                )
            }.collect { newState ->
                _uiState.value = newState
            }
        }
    }
    
    fun updateCycleLength(length: Int) {
        viewModelScope.launch {
            userPreferencesRepository.updateAverageCycleLength(length)
        }
    }
    
    fun updatePeriodLength(length: Int) {
        viewModelScope.launch {
            userPreferencesRepository.updateAveragePeriodLength(length)
        }
    }
    
    fun updateNotificationsEnabled(enabled: Boolean) {
        viewModelScope.launch {
            userPreferencesRepository.updateNotificationsEnabled(enabled)
        }
    }
    
    fun updatePeriodReminderDays(days: Int) {
        viewModelScope.launch {
            userPreferencesRepository.updatePeriodReminderDays(days)
        }
    }
    
    fun updateFertileWindowReminder(enabled: Boolean) {
        viewModelScope.launch {
            userPreferencesRepository.updateFertileWindowReminder(enabled)
        }
    }
    
    fun updateTheme(theme: String) {
        viewModelScope.launch {
            userPreferencesRepository.updateTheme(theme)
        }
    }
    
    fun updatePrivacyMode(enabled: Boolean) {
        viewModelScope.launch {
            userPreferencesRepository.updatePrivacyMode(enabled)
        }
    }
    
    fun addEmergencyContact(contact: EmergencyContact) {
        viewModelScope.launch {
            emergencyContactDao.insertEmergencyContact(contact)
        }
    }
    
    fun updateEmergencyContact(contact: EmergencyContact) {
        viewModelScope.launch {
            emergencyContactDao.updateEmergencyContact(contact)
        }
    }
    
    fun deleteEmergencyContact(contact: EmergencyContact) {
        viewModelScope.launch {
            emergencyContactDao.deleteEmergencyContact(contact)
        }
    }
    
    fun showAddContactDialog() {
        _uiState.value = _uiState.value.copy(showAddContactDialog = true)
    }
    
    fun hideAddContactDialog() {
        _uiState.value = _uiState.value.copy(showAddContactDialog = false)
    }
    
    fun showEditContactDialog(contact: EmergencyContact) {
        _uiState.value = _uiState.value.copy(
            showEditContactDialog = true,
            editingContact = contact
        )
    }
    
    fun hideEditContactDialog() {
        _uiState.value = _uiState.value.copy(
            showEditContactDialog = false,
            editingContact = null
        )
    }
}

data class SettingsUiState(
    val isLoading: Boolean = true,
    val userPreferences: UserPreferences? = null,
    val emergencyContacts: List<EmergencyContact> = emptyList(),
    val showAddContactDialog: Boolean = false,
    val showEditContactDialog: Boolean = false,
    val editingContact: EmergencyContact? = null,
    val error: String? = null
)
