package com.periodpal.app.ui.insights

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.TrendingDown
import androidx.compose.material.icons.filled.TrendingFlat
import androidx.compose.material.icons.filled.TrendingUp
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Icon
import androidx.compose.material3.LinearProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.periodpal.app.data.entities.SymptomType
import com.periodpal.app.ui.theme.FollicularGreen
import com.periodpal.app.ui.theme.LutealBlue
import com.periodpal.app.ui.theme.OvulationOrange
import com.periodpal.app.ui.theme.PeriodPalTheme
import com.periodpal.app.ui.theme.PeriodRed
import kotlinx.datetime.LocalDate

@Composable
fun InsightsScreen(
    modifier: Modifier = Modifier,
    viewModel: InsightsViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    
    InsightsContent(
        uiState = uiState,
        modifier = modifier
    )
}

@Composable
fun InsightsContent(
    uiState: InsightsUiState,
    modifier: Modifier = Modifier
) {
    if (uiState.isLoading) {
        Box(
            modifier = modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            CircularProgressIndicator(color = MaterialTheme.colorScheme.primary)
        }
        return
    }
    
    LazyColumn(
        modifier = modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        item {
            Text(
                text = "Insights & Analytics",
                style = MaterialTheme.typography.headlineMedium,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.onBackground
            )
        }
        
        item {
            PredictionsCard(predictions = uiState.predictions)
        }
        
        item {
            CycleStatisticsCard(statistics = uiState.cycleStatistics)
        }
        
        item {
            CycleRegularityCard(regularity = uiState.cycleRegularity)
        }
        
        if (uiState.commonSymptoms.isNotEmpty()) {
            item {
                Text(
                    text = "Most Common Symptoms",
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.SemiBold,
                    color = MaterialTheme.colorScheme.onBackground
                )
            }
            
            items(uiState.commonSymptoms) { symptom ->
                CommonSymptomCard(symptom = symptom)
            }
        }
        
        if (uiState.symptomTrends.isNotEmpty()) {
            item {
                Text(
                    text = "Symptom Trends",
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.SemiBold,
                    color = MaterialTheme.colorScheme.onBackground
                )
            }
            
            items(uiState.symptomTrends.toList()) { (_, trend) ->
                SymptomTrendCard(trend = trend)
            }
        }
        
        item {
            Spacer(modifier = Modifier.height(80.dp))
        }
    }
}

@Composable
fun PredictionsCard(
    predictions: PredictionData,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.primaryContainer
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(
                text = "Predictions",
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.onPrimaryContainer
            )
            
            predictions.nextPeriodDate?.let { date ->
                PredictionItem(
                    label = "Next Period",
                    value = date.toString(),
                    daysAway = predictions.daysUntilNextPeriod,
                    color = PeriodRed
                )
            }
            
            predictions.ovulationDate?.let { date ->
                PredictionItem(
                    label = "Next Ovulation",
                    value = date.toString(),
                    color = OvulationOrange
                )
            }
            
            if (predictions.fertileWindowStart != null && predictions.fertileWindowEnd != null) {
                PredictionItem(
                    label = "Fertile Window",
                    value = "${predictions.fertileWindowStart} - ${predictions.fertileWindowEnd}",
                    color = FollicularGreen
                )
            }
        }
    }
}

@Composable
fun PredictionItem(
    label: String,
    value: String,
    daysAway: Int? = null,
    color: Color,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Box(
                modifier = Modifier
                    .size(12.dp)
                    .clip(CircleShape)
                    .background(color)
            )
            Text(
                text = label,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onPrimaryContainer
            )
        }
        
        Column(horizontalAlignment = Alignment.End) {
            Text(
                text = value,
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.SemiBold,
                color = MaterialTheme.colorScheme.onPrimaryContainer
            )
            daysAway?.let { days ->
                Text(
                    text = if (days > 0) "in $days days" else if (days == 0) "today" else "${-days} days ago",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.7f)
                )
            }
        }
    }
}

@Composable
fun CycleStatisticsCard(
    statistics: CycleStatistics,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(
                text = "Cycle Statistics",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.SemiBold
            )
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                StatisticItem(
                    label = "Average Cycle",
                    value = "${statistics.averageCycleLength.toInt()} days",
                    modifier = Modifier.weight(1f)
                )
                StatisticItem(
                    label = "Average Period",
                    value = "${statistics.averagePeriodLength.toInt()} days",
                    modifier = Modifier.weight(1f)
                )
            }
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                StatisticItem(
                    label = "Shortest Cycle",
                    value = "${statistics.shortestCycle} days",
                    modifier = Modifier.weight(1f)
                )
                StatisticItem(
                    label = "Longest Cycle",
                    value = "${statistics.longestCycle} days",
                    modifier = Modifier.weight(1f)
                )
            }
            
            StatisticItem(
                label = "Total Cycles Tracked",
                value = statistics.totalCycles.toString()
            )
        }
    }
}

@Composable
fun StatisticItem(
    label: String,
    value: String,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = value,
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.primary
        )
        Text(
            text = label,
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

@Composable
fun CycleRegularityCard(
    regularity: CycleRegularity,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Text(
                text = "Cycle Regularity",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.SemiBold
            )
            
            val (regularityText, regularityColor, progress) = when (regularity) {
                CycleRegularity.VERY_REGULAR -> Triple("Very Regular", FollicularGreen, 1.0f)
                CycleRegularity.REGULAR -> Triple("Regular", FollicularGreen, 0.8f)
                CycleRegularity.SOMEWHAT_IRREGULAR -> Triple("Somewhat Irregular", OvulationOrange, 0.6f)
                CycleRegularity.IRREGULAR -> Triple("Irregular", PeriodRed, 0.4f)
                CycleRegularity.UNKNOWN -> Triple("Unknown", MaterialTheme.colorScheme.onSurfaceVariant, 0.0f)
            }
            
            Text(
                text = regularityText,
                style = MaterialTheme.typography.bodyLarge,
                fontWeight = FontWeight.Medium,
                color = regularityColor
            )
            
            LinearProgressIndicator(
                progress = progress,
                modifier = Modifier
                    .fillMaxWidth()
                    .height(8.dp)
                    .clip(RoundedCornerShape(4.dp)),
                color = regularityColor,
                trackColor = MaterialTheme.colorScheme.surfaceVariant
            )
        }
    }
}

@Composable
fun CommonSymptomCard(
    symptom: CommonSymptom,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column {
                Text(
                    text = symptom.symptomType.name.replace("_", " ").lowercase()
                        .split(" ").joinToString(" ") { it.capitalize() },
                    style = MaterialTheme.typography.bodyLarge,
                    fontWeight = FontWeight.Medium
                )
                Text(
                    text = "Logged ${symptom.frequency} times",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
            
            Text(
                text = "Avg: ${symptom.averageIntensity.toInt()}",
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.SemiBold,
                color = MaterialTheme.colorScheme.primary
            )
        }
    }
}

@Composable
fun SymptomTrendCard(
    trend: SymptomTrend,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column {
                Text(
                    text = trend.symptomType.name.replace("_", " ").lowercase()
                        .split(" ").joinToString(" ") { it.capitalize() },
                    style = MaterialTheme.typography.bodyLarge,
                    fontWeight = FontWeight.Medium
                )
                Text(
                    text = "Average: ${trend.averageValue.toInt()}",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
            
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                val (icon, color) = when (trend.trendDirection) {
                    TrendDirection.INCREASING -> Icons.Default.TrendingUp to PeriodRed
                    TrendDirection.DECREASING -> Icons.Default.TrendingDown to FollicularGreen
                    TrendDirection.STABLE -> Icons.Default.TrendingFlat to MaterialTheme.colorScheme.onSurfaceVariant
                }
                
                Icon(
                    imageVector = icon,
                    contentDescription = trend.trendDirection.name,
                    tint = color,
                    modifier = Modifier.size(20.dp)
                )
                
                Text(
                    text = trend.trendDirection.name.lowercase().capitalize(),
                    style = MaterialTheme.typography.bodySmall,
                    color = color
                )
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun InsightsScreenPreview() {
    PeriodPalTheme {
        InsightsContent(
            uiState = InsightsUiState(
                isLoading = false,
                predictions = PredictionData(
                    nextPeriodDate = LocalDate(2024, 1, 15),
                    daysUntilNextPeriod = 5
                ),
                cycleRegularity = CycleRegularity.REGULAR
            )
        )
    }
}
