package com.periodpal.app.data.database

import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import android.content.Context
import com.periodpal.app.data.dao.CycleEntryDao
import com.periodpal.app.data.dao.EmergencyContactDao
import com.periodpal.app.data.dao.SymptomEntryDao
import com.periodpal.app.data.dao.UserPreferencesDao
import com.periodpal.app.data.entities.CycleEntry
import com.periodpal.app.data.entities.EmergencyContact
import com.periodpal.app.data.entities.SymptomEntry
import com.periodpal.app.data.entities.UserPreferences

@Database(
    entities = [
        CycleEntry::class,
        SymptomEntry::class,
        UserPreferences::class,
        EmergencyContact::class
    ],
    version = 1,
    exportSchema = true
)
@TypeConverters(Converters::class)
abstract class PeriodPalDatabase : RoomDatabase() {
    
    abstract fun cycleEntryDao(): CycleEntryDao
    abstract fun symptomEntryDao(): SymptomEntryDao
    abstract fun userPreferencesDao(): UserPreferencesDao
    abstract fun emergencyContactDao(): EmergencyContactDao
    
    companion object {
        @Volatile
        private var INSTANCE: PeriodPalDatabase? = null
        
        fun getDatabase(context: Context): PeriodPalDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    PeriodPalDatabase::class.java,
                    "period_pal_database"
                )
                    .fallbackToDestructiveMigration()
                    .build()
                INSTANCE = instance
                instance
            }
        }
    }
}
