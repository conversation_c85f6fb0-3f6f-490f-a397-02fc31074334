package com.periodpal.app.data.dao

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update
import com.periodpal.app.data.entities.EmergencyContact
import kotlinx.coroutines.flow.Flow

@Dao
interface EmergencyContactDao {
    
    @Query("SELECT * FROM emergency_contacts ORDER BY isPrimary DESC, name ASC")
    fun getAllEmergencyContacts(): Flow<List<EmergencyContact>>
    
    @Query("SELECT * FROM emergency_contacts WHERE id = :id LIMIT 1")
    suspend fun getEmergencyContactById(id: Long): EmergencyContact?
    
    @Query("SELECT * FROM emergency_contacts WHERE isPrimary = 1 ORDER BY createdAt ASC")
    suspend fun getPrimaryEmergencyContacts(): List<EmergencyContact>
    
    @Query("SELECT * FROM emergency_contacts WHERE relationship = :relationship ORDER BY name ASC")
    suspend fun getEmergencyContactsByRelationship(relationship: String): List<EmergencyContact>
    
    @Query("SELECT COUNT(*) FROM emergency_contacts")
    suspend fun getEmergencyContactsCount(): Int
    
    @Query("SELECT COUNT(*) FROM emergency_contacts WHERE isPrimary = 1")
    suspend fun getPrimaryContactsCount(): Int
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertEmergencyContact(emergencyContact: EmergencyContact): Long
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertEmergencyContacts(emergencyContacts: List<EmergencyContact>)
    
    @Update
    suspend fun updateEmergencyContact(emergencyContact: EmergencyContact)
    
    @Delete
    suspend fun deleteEmergencyContact(emergencyContact: EmergencyContact)
    
    @Query("DELETE FROM emergency_contacts WHERE id = :id")
    suspend fun deleteEmergencyContactById(id: Long)
    
    @Query("DELETE FROM emergency_contacts")
    suspend fun deleteAllEmergencyContacts()
    
    @Query("UPDATE emergency_contacts SET isPrimary = 0")
    suspend fun clearAllPrimaryFlags()
}
