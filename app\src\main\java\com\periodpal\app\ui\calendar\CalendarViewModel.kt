package com.periodpal.app.ui.calendar

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.periodpal.app.data.entities.CycleEntry
import com.periodpal.app.data.entities.SymptomEntry
import com.periodpal.app.data.entities.SymptomType
import com.periodpal.app.data.repository.CycleRepository
import com.periodpal.app.data.repository.UserPreferencesRepository
import com.periodpal.app.ui.components.CyclePhase
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.launch
import kotlinx.datetime.Clock
import kotlinx.datetime.DateTimeUnit
import kotlinx.datetime.LocalDate
import kotlinx.datetime.TimeZone
import kotlinx.datetime.minus
import kotlinx.datetime.plus
import kotlinx.datetime.todayIn
import javax.inject.Inject

@HiltViewModel
class CalendarViewModel @Inject constructor(
    private val cycleRepository: CycleRepository,
    private val userPreferencesRepository: UserPreferencesRepository
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(CalendarUiState())
    val uiState: StateFlow<CalendarUiState> = _uiState.asStateFlow()
    
    private val today = Clock.System.todayIn(TimeZone.currentSystemDefault())
    
    init {
        loadCalendarData()
    }
    
    private fun loadCalendarData() {
        viewModelScope.launch {
            combine(
                cycleRepository.getAllCycleEntries(),
                cycleRepository.getAllSymptomEntries(),
                userPreferencesRepository.getUserPreferences()
            ) { cycleEntries, symptomEntries, preferences ->
                val userPrefs = preferences ?: return@combine
                
                // Calculate date range for calendar (3 months back, 3 months forward)
                val startDate = today.minus(3, DateTimeUnit.MONTH)
                val endDate = today.plus(3, DateTimeUnit.MONTH)
                
                // Get cycle entries in range
                val cycleEntriesInRange = cycleRepository.getCycleEntriesInRange(startDate, endDate)
                val symptomEntriesInRange = cycleRepository.getSymptomEntriesInRange(startDate, endDate)
                
                // Calculate predictions
                val lastPeriodDay = cycleRepository.getLastPeriodDay()
                val predictions = calculatePredictions(lastPeriodDay, userPrefs, startDate, endDate)
                
                // Create calendar days
                val calendarDays = createCalendarDays(
                    cycleEntriesInRange,
                    symptomEntriesInRange,
                    predictions,
                    userPrefs,
                    startDate,
                    endDate
                )
                
                CalendarUiState(
                    isLoading = false,
                    currentMonth = today,
                    calendarDays = calendarDays,
                    selectedDate = null,
                    userPreferences = userPrefs
                )
            }.collect { newState ->
                _uiState.value = newState
            }
        }
    }
    
    private suspend fun calculatePredictions(
        lastPeriodDay: CycleEntry?,
        userPrefs: com.periodpal.app.data.entities.UserPreferences,
        startDate: LocalDate,
        endDate: LocalDate
    ): List<PredictionEntry> {
        val predictions = mutableListOf<PredictionEntry>()
        
        lastPeriodDay?.let { lastPeriod ->
            var currentPredictionDate = lastPeriod.date
            
            // Generate predictions for the next 6 cycles
            repeat(6) {
                currentPredictionDate = currentPredictionDate.plus(userPrefs.averageCycleLength, DateTimeUnit.DAY)
                
                if (currentPredictionDate >= startDate && currentPredictionDate <= endDate) {
                    // Period prediction
                    repeat(userPrefs.averagePeriodLength) { dayOffset ->
                        val periodDate = currentPredictionDate.plus(dayOffset, DateTimeUnit.DAY)
                        if (periodDate <= endDate) {
                            predictions.add(
                                PredictionEntry(
                                    date = periodDate,
                                    type = PredictionType.PERIOD,
                                    confidence = 0.8f - (it * 0.1f) // Confidence decreases over time
                                )
                            )
                        }
                    }
                    
                    // Fertile window prediction (5 days before ovulation + ovulation day)
                    val ovulationDate = currentPredictionDate.plus(
                        userPrefs.averageCycleLength - userPrefs.lutealPhaseLength,
                        DateTimeUnit.DAY
                    )
                    
                    repeat(6) { dayOffset ->
                        val fertileDate = ovulationDate.minus(5 - dayOffset, DateTimeUnit.DAY)
                        if (fertileDate >= startDate && fertileDate <= endDate) {
                            predictions.add(
                                PredictionEntry(
                                    date = fertileDate,
                                    type = if (dayOffset == 5) PredictionType.OVULATION else PredictionType.FERTILE,
                                    confidence = 0.7f - (it * 0.1f)
                                )
                            )
                        }
                    }
                    
                    // PMS prediction (5 days before period)
                    repeat(5) { dayOffset ->
                        val pmsDate = currentPredictionDate.minus(5 - dayOffset, DateTimeUnit.DAY)
                        if (pmsDate >= startDate && pmsDate <= endDate) {
                            predictions.add(
                                PredictionEntry(
                                    date = pmsDate,
                                    type = PredictionType.PMS,
                                    confidence = 0.6f - (it * 0.1f)
                                )
                            )
                        }
                    }
                }
            }
        }
        
        return predictions
    }
    
    private suspend fun createCalendarDays(
        cycleEntries: List<CycleEntry>,
        symptomEntries: List<SymptomEntry>,
        predictions: List<PredictionEntry>,
        userPrefs: com.periodpal.app.data.entities.UserPreferences,
        startDate: LocalDate,
        endDate: LocalDate
    ): List<CalendarDay> {
        val calendarDays = mutableListOf<CalendarDay>()
        val cycleEntriesMap = cycleEntries.associateBy { it.date }
        val symptomEntriesMap = symptomEntries.groupBy { it.date }
        val predictionsMap = predictions.groupBy { it.date }
        
        var currentDate = startDate
        while (currentDate <= endDate) {
            val cycleEntry = cycleEntriesMap[currentDate]
            val daySymptoms = symptomEntriesMap[currentDate] ?: emptyList()
            val dayPredictions = predictionsMap[currentDate] ?: emptyList()
            
            calendarDays.add(
                CalendarDay(
                    date = currentDate,
                    isPeriodDay = cycleEntry?.isPeriodDay == true,
                    isOvulationDay = cycleEntry?.isOvulationDay == true,
                    cycleDay = cycleEntry?.cycleDay,
                    symptoms = daySymptoms,
                    predictions = dayPredictions,
                    isToday = currentDate == today,
                    hasData = cycleEntry != null || daySymptoms.isNotEmpty()
                )
            )
            
            currentDate = currentDate.plus(1, DateTimeUnit.DAY)
        }
        
        return calendarDays
    }
    
    fun selectDate(date: LocalDate) {
        _uiState.value = _uiState.value.copy(selectedDate = date)
    }
    
    fun togglePeriodDay(date: LocalDate) {
        viewModelScope.launch {
            val existingEntry = cycleRepository.getCycleEntryByDate(date)
            if (existingEntry?.isPeriodDay == true) {
                cycleRepository.removePeriodDay(date)
            } else {
                cycleRepository.logPeriodDay(date)
            }
        }
    }
    
    fun navigateToMonth(month: LocalDate) {
        _uiState.value = _uiState.value.copy(currentMonth = month)
    }
}

data class CalendarUiState(
    val isLoading: Boolean = true,
    val currentMonth: LocalDate = Clock.System.todayIn(TimeZone.currentSystemDefault()),
    val calendarDays: List<CalendarDay> = emptyList(),
    val selectedDate: LocalDate? = null,
    val userPreferences: com.periodpal.app.data.entities.UserPreferences? = null,
    val error: String? = null
)

data class CalendarDay(
    val date: LocalDate,
    val isPeriodDay: Boolean = false,
    val isOvulationDay: Boolean = false,
    val cycleDay: Int? = null,
    val symptoms: List<SymptomEntry> = emptyList(),
    val predictions: List<PredictionEntry> = emptyList(),
    val isToday: Boolean = false,
    val hasData: Boolean = false
)

data class PredictionEntry(
    val date: LocalDate,
    val type: PredictionType,
    val confidence: Float // 0.0 to 1.0
)

enum class PredictionType {
    PERIOD,
    FERTILE,
    OVULATION,
    PMS
}
