# PeriodPal - Menstrual Cycle Tracking App

A comprehensive Android application for tracking women's menstrual cycles with a clean, feminine-friendly interface designed with accessibility and user experience in mind.

## Features

### 🌸 Clean, Feminine Design
- Rose pink and white color scheme for a comfortable user experience
- Soft, calming colors with excellent contrast ratios
- Material Design 3 principles with custom theming

### 📅 Comprehensive Cycle Tracking
- **Circular Calendar Widget**: Visual representation of current cycle day and phase
- **Monthly Calendar View**: Period days, fertile window, ovulation, and PMS days highlighting
- **Cycle Predictions**: AI-powered predictions for next period, ovulation, and fertile windows
- **Phase Tracking**: Automatic detection of menstrual, follicular, ovulation, and luteal phases

### 📊 Symptom Logging
- **Quick-tap Icons**: Easy logging for flow intensity, mood, and pain level
- **Comprehensive Symptoms**: Track 15+ different symptoms including:
  - Flow intensity (light, medium, heavy)
  - Mood (happy, sad, irritable, anxious)
  - Pain levels (none to severe)
  - Energy levels, sleep quality, appetite
  - Cramps, headaches, bloating, breast tenderness
  - Acne, nausea, back pain, and more
- **Daily Health Logs**: Historical view of all logged symptoms

### 📈 Health Insights & Analytics
- **Cycle Statistics**: Average cycle length, period length, cycle regularity
- **Symptom Trends**: Track patterns and changes over time
- **Predictions Dashboard**: Next period, ovulation, and fertile window forecasts
- **Regularity Analysis**: Understand your cycle patterns

### 🔔 Smart Notifications
- **Period Reminders**: Customizable alerts 1-7 days before expected period
- **Fertile Window Alerts**: Notifications for optimal conception timing
- **Ovulation Reminders**: Daily notifications during ovulation
- **Symptom Logging Reminders**: Gentle reminders to log daily symptoms

### 🚨 Emergency Contacts
- **Quick Access**: Store important healthcare provider contacts
- **Relationship Categories**: Doctor, partner, family, friend classifications
- **Primary Contact**: Designate most important emergency contact
- **One-tap Calling**: Direct calling from the app

### ⚙️ Customizable Settings
- **Cycle Preferences**: Adjust average cycle and period length
- **Notification Controls**: Fine-tune reminder preferences
- **Privacy Mode**: Hide sensitive information on lock screen
- **Theme Options**: Light/dark mode support
- **Accessibility**: Full screen reader support and high contrast options

## Technical Architecture

### 🏗️ Modern Android Development
- **Jetpack Compose**: Modern declarative UI framework
- **Material Design 3**: Latest design system implementation
- **MVVM Architecture**: Clean separation of concerns
- **Hilt Dependency Injection**: Efficient dependency management

### 💾 Data Management
- **Room Database**: Local data persistence with type-safe queries
- **Kotlin Coroutines**: Asynchronous programming for smooth UI
- **Flow**: Reactive data streams for real-time updates
- **Repository Pattern**: Clean data layer abstraction

### 📱 User Experience
- **Accessibility First**: WCAG AA compliance with 4.5:1 contrast ratios
- **Touch Targets**: Minimum 44x44dp touch targets for easy interaction
- **Screen Reader Support**: Comprehensive content descriptions and semantic labels
- **Smooth Animations**: Spring-based animations for delightful interactions

### 🔒 Privacy & Security
- **Local Storage**: All data stored locally on device
- **No Cloud Sync**: Complete privacy with no external data transmission
- **Privacy Mode**: Hide sensitive information when needed
- **Secure Backup**: Optional encrypted local backups

## Installation

1. Clone the repository:
```bash
git clone https://github.com/yourusername/PeriodPal.git
```

2. Open in Android Studio
3. Build and run on your Android device or emulator

## Requirements

- **Minimum SDK**: Android 7.0 (API level 24)
- **Target SDK**: Android 14 (API level 34)
- **Kotlin**: 1.9.20+
- **Gradle**: 8.2.0+

## Dependencies

- **Jetpack Compose**: UI framework
- **Room**: Database
- **Hilt**: Dependency injection
- **Navigation Compose**: Navigation
- **Work Manager**: Background tasks
- **Kotlin DateTime**: Date/time handling

## Contributing

We welcome contributions! Please read our contributing guidelines and submit pull requests for any improvements.

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- Material Design 3 guidelines
- WCAG accessibility standards
- Android Jetpack libraries
- Kotlin coroutines and flow

## Support

For support, please open an issue on GitHub or contact our support team.

---

**PeriodPal** - Empowering women with comprehensive, private, and accessible menstrual health tracking. 🌸
