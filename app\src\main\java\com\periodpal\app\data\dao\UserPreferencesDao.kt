package com.periodpal.app.data.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update
import com.periodpal.app.data.entities.UserPreferences
import kotlinx.coroutines.flow.Flow

@Dao
interface UserPreferencesDao {
    
    @Query("SELECT * FROM user_preferences WHERE id = 1 LIMIT 1")
    fun getUserPreferences(): Flow<UserPreferences?>
    
    @Query("SELECT * FROM user_preferences WHERE id = 1 LIMIT 1")
    suspend fun getUserPreferencesOnce(): UserPreferences?
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertUserPreferences(userPreferences: UserPreferences)
    
    @Update
    suspend fun updateUserPreferences(userPreferences: UserPreferences)
    
    @Query("UPDATE user_preferences SET averageCycleLength = :cycleLength WHERE id = 1")
    suspend fun updateAverageCycleLength(cycleLength: Int)
    
    @Query("UPDATE user_preferences SET averagePeriodLength = :periodLength WHERE id = 1")
    suspend fun updateAveragePeriodLength(periodLength: Int)
    
    @Query("UPDATE user_preferences SET notificationsEnabled = :enabled WHERE id = 1")
    suspend fun updateNotificationsEnabled(enabled: Boolean)
    
    @Query("UPDATE user_preferences SET periodReminderDays = :days WHERE id = 1")
    suspend fun updatePeriodReminderDays(days: Int)
    
    @Query("UPDATE user_preferences SET fertileWindowReminder = :enabled WHERE id = 1")
    suspend fun updateFertileWindowReminder(enabled: Boolean)
    
    @Query("UPDATE user_preferences SET theme = :theme WHERE id = 1")
    suspend fun updateTheme(theme: String)
    
    @Query("UPDATE user_preferences SET privacyMode = :enabled WHERE id = 1")
    suspend fun updatePrivacyMode(enabled: Boolean)
    
    @Query("DELETE FROM user_preferences")
    suspend fun deleteAllUserPreferences()
}
