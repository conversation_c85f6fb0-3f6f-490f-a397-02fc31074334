package com.periodpal.app.ui.components

import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp

/**
 * Accessibility-compliant button with minimum touch target size
 */
@Composable
fun AccessibleButton(
    text: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    contentDescription: String? = null,
    backgroundColor: Color = MaterialTheme.colorScheme.primary,
    contentColor: Color = MaterialTheme.colorScheme.onPrimary
) {
    Button(
        onClick = onClick,
        enabled = enabled,
        modifier = modifier
            .size(minWidth = 44.dp, minHeight = 44.dp)
            .semantics {
                contentDescription?.let { this.contentDescription = it }
            },
        shape = RoundedCornerShape(12.dp),
        colors = ButtonDefaults.buttonColors(
            containerColor = backgroundColor,
            contentColor = contentColor
        )
    ) {
        Text(
            text = text,
            style = MaterialTheme.typography.labelLarge,
            fontWeight = FontWeight.SemiBold
        )
    }
}

/**
 * High contrast text for better accessibility
 */
@Composable
fun AccessibleText(
    text: String,
    modifier: Modifier = Modifier,
    style: androidx.compose.ui.text.TextStyle = MaterialTheme.typography.bodyMedium,
    color: Color = MaterialTheme.colorScheme.onSurface,
    contentDescription: String? = null
) {
    Text(
        text = text,
        modifier = modifier.semantics {
            contentDescription?.let { this.contentDescription = it }
        },
        style = style,
        color = color
    )
}

/**
 * Accessibility helper for screen readers
 */
object AccessibilityStrings {
    const val PERIOD_DAY_BUTTON = "Mark as period day"
    const val SYMPTOM_LOGGING = "Log symptom"
    const val CALENDAR_DAY = "Calendar day"
    const val NAVIGATION_DASHBOARD = "Navigate to dashboard"
    const val NAVIGATION_CALENDAR = "Navigate to calendar"
    const val NAVIGATION_HEALTH_LOGS = "Navigate to health logs"
    const val NAVIGATION_INSIGHTS = "Navigate to insights"
    const val NAVIGATION_SETTINGS = "Navigate to settings"
    const val CYCLE_PHASE_INDICATOR = "Current cycle phase indicator"
    const val SYMPTOM_INTENSITY = "Symptom intensity level"
    const val EMERGENCY_CONTACT = "Emergency contact"
    const val ADD_CONTACT = "Add emergency contact"
    const val EDIT_CONTACT = "Edit emergency contact"
    const val DELETE_CONTACT = "Delete emergency contact"
}
