package com.periodpal.app.ui.dashboard

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Face
import androidx.compose.material.icons.filled.Favorite
import androidx.compose.material.icons.filled.LocalHospital
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.periodpal.app.R
import com.periodpal.app.ui.components.ButtonVariant
import com.periodpal.app.ui.components.CyclePhase
import com.periodpal.app.ui.components.CyclePhaseIndicator
import com.periodpal.app.ui.components.PeriodPalButton
import com.periodpal.app.ui.components.SymptomCard
import com.periodpal.app.ui.components.SymptomOptions
import com.periodpal.app.ui.theme.PeriodPalTheme
import kotlinx.datetime.LocalDate

@Composable
fun DashboardScreen(
    modifier: Modifier = Modifier,
    viewModel: DashboardViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    
    DashboardContent(
        uiState = uiState,
        onTogglePeriodDay = viewModel::togglePeriodDay,
        onFlowIntensitySelected = viewModel::logFlowIntensity,
        onMoodSelected = viewModel::logMood,
        onPainLevelSelected = viewModel::logPainLevel,
        modifier = modifier
    )
}

@Composable
fun DashboardContent(
    uiState: DashboardUiState,
    onTogglePeriodDay: () -> Unit,
    onFlowIntensitySelected: (com.periodpal.app.ui.components.SymptomOption) -> Unit,
    onMoodSelected: (com.periodpal.app.ui.components.SymptomOption) -> Unit,
    onPainLevelSelected: (com.periodpal.app.ui.components.SymptomOption) -> Unit,
    modifier: Modifier = Modifier
) {
    if (uiState.isLoading) {
        Box(
            modifier = modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            CircularProgressIndicator(color = MaterialTheme.colorScheme.primary)
        }
        return
    }
    
    Column(
        modifier = modifier
            .fillMaxSize()
            .verticalScroll(rememberScrollState())
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(20.dp)
    ) {
        // Header with current date
        Text(
            text = "Today",
            style = MaterialTheme.typography.headlineMedium,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.onBackground
        )
        
        // Cycle Phase Indicator
        CyclePhaseIndicator(
            currentDay = uiState.currentDay,
            cycleLength = uiState.cycleLength,
            periodLength = uiState.periodLength,
            currentPhase = uiState.currentPhase
        )
        
        // Quick Action Buttons
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            PeriodPalButton(
                text = if (uiState.isPeriodDay) "End Period" else "Start Period",
                onClick = onTogglePeriodDay,
                variant = if (uiState.isPeriodDay) ButtonVariant.Secondary else ButtonVariant.Primary,
                modifier = Modifier.weight(1f)
            )
        }
        
        // Symptom Tracking Section
        Text(
            text = "Log Today's Symptoms",
            style = MaterialTheme.typography.titleLarge,
            fontWeight = FontWeight.SemiBold,
            color = MaterialTheme.colorScheme.onBackground
        )
        
        // Flow Intensity Card
        if (uiState.isPeriodDay) {
            SymptomCard(
                title = "Flow Intensity",
                icon = Icons.Default.Favorite,
                options = SymptomOptions.flowOptions,
                selectedOption = uiState.selectedFlowOption,
                onOptionSelected = onFlowIntensitySelected
            )
        }
        
        // Mood Card
        SymptomCard(
            title = "Mood",
            icon = Icons.Default.Face,
            options = SymptomOptions.moodOptions,
            selectedOption = uiState.selectedMoodOption,
            onOptionSelected = onMoodSelected
        )
        
        // Pain Level Card
        SymptomCard(
            title = "Pain Level",
            icon = Icons.Default.LocalHospital,
            options = SymptomOptions.painOptions,
            selectedOption = uiState.selectedPainOption,
            onOptionSelected = onPainLevelSelected
        )
        
        // Predictions Section
        if (uiState.nextPeriodDate != null || uiState.fertileWindowStart != null) {
            Text(
                text = "Predictions",
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.SemiBold,
                color = MaterialTheme.colorScheme.onBackground
            )
            
            PredictionsCard(
                nextPeriodDate = uiState.nextPeriodDate,
                fertileWindowStart = uiState.fertileWindowStart,
                fertileWindowEnd = uiState.fertileWindowEnd,
                lastPeriodDate = uiState.lastPeriodDate
            )
        }
        
        // Add some bottom padding for better scrolling
        Spacer(modifier = Modifier.height(80.dp))
    }
}

@Composable
fun PredictionsCard(
    nextPeriodDate: LocalDate?,
    fertileWindowStart: LocalDate?,
    fertileWindowEnd: LocalDate?,
    lastPeriodDate: LocalDate?,
    modifier: Modifier = Modifier
) {
    androidx.compose.material3.Card(
        modifier = modifier.fillMaxWidth(),
        colors = androidx.compose.material3.CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            lastPeriodDate?.let { date ->
                PredictionItem(
                    label = stringResource(R.string.last_period),
                    value = date.toString()
                )
            }
            
            nextPeriodDate?.let { date ->
                PredictionItem(
                    label = stringResource(R.string.next_period_prediction),
                    value = date.toString()
                )
            }
            
            if (fertileWindowStart != null && fertileWindowEnd != null) {
                PredictionItem(
                    label = stringResource(R.string.fertile_window),
                    value = "${fertileWindowStart} - ${fertileWindowEnd}"
                )
            }
        }
    }
}

@Composable
fun PredictionItem(
    label: String,
    value: String,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = label,
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        Text(
            text = value,
            style = MaterialTheme.typography.bodyMedium,
            fontWeight = FontWeight.SemiBold,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

@Preview(showBackground = true)
@Composable
fun DashboardScreenPreview() {
    PeriodPalTheme {
        DashboardContent(
            uiState = DashboardUiState(
                isLoading = false,
                currentDay = 8,
                cycleLength = 28,
                periodLength = 5,
                currentPhase = CyclePhase.FOLLICULAR,
                isPeriodDay = false,
                nextPeriodDate = LocalDate(2024, 1, 15),
                fertileWindowStart = LocalDate(2024, 1, 10),
                fertileWindowEnd = LocalDate(2024, 1, 14),
                lastPeriodDate = LocalDate(2023, 12, 18)
            ),
            onTogglePeriodDay = { },
            onFlowIntensitySelected = { },
            onMoodSelected = { },
            onPainLevelSelected = { }
        )
    }
}
