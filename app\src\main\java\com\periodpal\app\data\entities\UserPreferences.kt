package com.periodpal.app.data.entities

import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "user_preferences")
data class UserPreferences(
    @PrimaryKey
    val id: Long = 1, // Single row for user preferences
    val averageCycleLength: Int = 28,
    val averagePeriodLength: Int = 5,
    val lutealPhaseLength: Int = 14,
    val notificationsEnabled: Boolean = true,
    val periodReminderDays: Int = 2, // Days before period to remind
    val fertileWindowReminder: Boolean = true,
    val ovulationReminderDays: Int = 1,
    val theme: String = "LIGHT", // LIGHT, DARK, SYSTEM
    val firstDayOfWeek: Int = 1, // 1 = Monday, 7 = Sunday
    val temperatureUnit: String = "CELSIUS", // CELSIUS, FAHRENHEIT
    val dateFormat: String = "DD/MM/YYYY",
    val privacyMode: Boolean = false,
    val backupEnabled: Boolean = true,
    val createdAt: kotlinx.datetime.Instant = kotlinx.datetime.Clock.System.now(),
    val updatedAt: kotlinx.datetime.Instant = kotlinx.datetime.Clock.System.now()
)
