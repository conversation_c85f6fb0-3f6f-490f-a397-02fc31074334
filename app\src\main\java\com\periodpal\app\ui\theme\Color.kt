package com.periodpal.app.ui.theme

import androidx.compose.ui.graphics.Color

// Primary Colors - Rose Pink Theme
val RosePink = Color(0xFFF8BBD9)
val RosePinkDark = Color(0xFFE91E63)
val RosePinkLight = Color(0xFFFCE4EC)

// Secondary Colors
val SoftPurple = Color(0xFFE1BEE7)
val Lavender = Color(0xFFF3E5F5)
val CreamWhite = Color(0xFFFFFBFE)

// Cycle Phase Colors
val PeriodRed = Color(0xFFD32F2F)
val FollicularGreen = Color(0xFF388E3C)
val OvulationOrange = Color(0xFFF57C00)
val LutealBlue = Color(0xFF1976D2)

// Symptom Colors
val FlowLight = Color(0xFFFFCDD2)
val FlowMedium = Color(0xFFEF5350)
val FlowHeavy = Color(0xFFC62828)

// Mood Colors
val MoodHappy = Color(0xFF4CAF50)
val MoodSad = Color(0xFF2196F3)
val MoodIrritable = Color(0xFFFF9800)
val MoodAnxious = Color(0xFF9C27B0)

// Pain Colors
val PainNone = Color(0xFFE8F5E8)
val PainMild = Color(0xFFFFF3E0)
val PainModerate = Color(0xFFFFE0B2)
val PainSevere = Color(0xFFFFCDD2)

// System Colors
val GrayLight = Color(0xFFF5F5F5)
val GrayMedium = Color(0xFF9E9E9E)
val GrayDark = Color(0xFF424242)

// Material 3 Color Scheme
val md_theme_light_primary = RosePink
val md_theme_light_onPrimary = Color(0xFFFFFFFF)
val md_theme_light_primaryContainer = RosePinkLight
val md_theme_light_onPrimaryContainer = RosePinkDark
val md_theme_light_secondary = SoftPurple
val md_theme_light_onSecondary = Color(0xFFFFFFFF)
val md_theme_light_secondaryContainer = Lavender
val md_theme_light_onSecondaryContainer = Color(0xFF4A148C)
val md_theme_light_tertiary = Color(0xFF7D5260)
val md_theme_light_onTertiary = Color(0xFFFFFFFF)
val md_theme_light_tertiaryContainer = Color(0xFFFFD8E4)
val md_theme_light_onTertiaryContainer = Color(0xFF31111D)
val md_theme_light_error = Color(0xFFBA1A1A)
val md_theme_light_errorContainer = Color(0xFFFFDAD6)
val md_theme_light_onError = Color(0xFFFFFFFF)
val md_theme_light_onErrorContainer = Color(0xFF410002)
val md_theme_light_background = CreamWhite
val md_theme_light_onBackground = GrayDark
val md_theme_light_surface = Color(0xFFFFFFFF)
val md_theme_light_onSurface = GrayDark
val md_theme_light_surfaceVariant = Color(0xFFF2DDE1)
val md_theme_light_onSurfaceVariant = Color(0xFF514347)
val md_theme_light_outline = Color(0xFF837377)
val md_theme_light_inverseOnSurface = Color(0xFFFAEEEF)
val md_theme_light_inverseSurface = Color(0xFF362F30)
val md_theme_light_inversePrimary = Color(0xFFFFB1C8)
val md_theme_light_shadow = Color(0xFF000000)
val md_theme_light_surfaceTint = RosePink
val md_theme_light_outlineVariant = Color(0xFFD5C2C6)
val md_theme_light_scrim = Color(0xFF000000)
