package com.periodpal.app.data.dao

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update
import com.periodpal.app.data.entities.CycleEntry
import kotlinx.coroutines.flow.Flow
import kotlinx.datetime.LocalDate

@Dao
interface CycleEntryDao {
    
    @Query("SELECT * FROM cycle_entries ORDER BY date DESC")
    fun getAllCycleEntries(): Flow<List<CycleEntry>>
    
    @Query("SELECT * FROM cycle_entries WHERE date = :date LIMIT 1")
    suspend fun getCycleEntryByDate(date: LocalDate): CycleEntry?
    
    @Query("SELECT * FROM cycle_entries WHERE date BETWEEN :startDate AND :endDate ORDER BY date ASC")
    suspend fun getCycleEntriesInRange(startDate: LocalDate, endDate: LocalDate): List<CycleEntry>
    
    @Query("SELECT * FROM cycle_entries WHERE isPeriodDay = 1 ORDER BY date DESC")
    fun getPeriodDays(): Flow<List<CycleEntry>>
    
    @Query("SELECT * FROM cycle_entries WHERE isPeriodDay = 1 ORDER BY date DESC LIMIT 1")
    suspend fun getLastPeriodDay(): CycleEntry?
    
    @Query("SELECT * FROM cycle_entries WHERE isOvulationDay = 1 ORDER BY date DESC")
    fun getOvulationDays(): Flow<List<CycleEntry>>
    
    @Query("SELECT * FROM cycle_entries WHERE date >= :fromDate ORDER BY date ASC")
    suspend fun getCycleEntriesFrom(fromDate: LocalDate): List<CycleEntry>
    
    @Query("SELECT COUNT(*) FROM cycle_entries WHERE isPeriodDay = 1")
    suspend fun getPeriodDaysCount(): Int
    
    @Query("""
        SELECT AVG(cycle_length) FROM (
            SELECT 
                JULIANDAY(next_period.date) - JULIANDAY(current_period.date) as cycle_length
            FROM 
                (SELECT date, ROW_NUMBER() OVER (ORDER BY date) as rn 
                 FROM cycle_entries 
                 WHERE isPeriodDay = 1 
                 GROUP BY date) current_period
            JOIN 
                (SELECT date, ROW_NUMBER() OVER (ORDER BY date) as rn 
                 FROM cycle_entries 
                 WHERE isPeriodDay = 1 
                 GROUP BY date) next_period
            ON current_period.rn = next_period.rn - 1
            LIMIT 6
        )
    """)
    suspend fun getAverageCycleLength(): Double?
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertCycleEntry(cycleEntry: CycleEntry): Long
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertCycleEntries(cycleEntries: List<CycleEntry>)
    
    @Update
    suspend fun updateCycleEntry(cycleEntry: CycleEntry)
    
    @Delete
    suspend fun deleteCycleEntry(cycleEntry: CycleEntry)
    
    @Query("DELETE FROM cycle_entries WHERE date = :date")
    suspend fun deleteCycleEntryByDate(date: LocalDate)
    
    @Query("DELETE FROM cycle_entries")
    suspend fun deleteAllCycleEntries()
}
